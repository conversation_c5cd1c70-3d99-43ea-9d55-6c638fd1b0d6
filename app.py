"""
Medical Report Tracker - Main Flask Application
"""
import os
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
from datetime import datetime
import json

from services.pdf_processor import PDFProcessor
from services.database import db, Report, Parameter
from config import Config

def create_app():
    app = Flask(__name__)

    # Initialize config
    config = Config()
    app.config.from_object(config)

    # Initialize database
    db.init_app(app)

    # Create tables
    with app.app_context():
        db.create_all()

    return app

app = create_app()

# Initialize PDF processor only when needed to avoid environment issues
def get_pdf_processor():
    return PDFProcessor()

@app.route('/')
def index():
    """Main dashboard showing recent reports"""
    recent_reports = Report.query.order_by(Report.upload_date.desc()).limit(10).all()
    return render_template('index.html', reports=recent_reports)

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """Handle file upload and processing"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                # Process the PDF
                pdf_processor = get_pdf_processor()
                parameters = pdf_processor.process_single_report(filepath)
                
                # Save to database
                report = Report(
                    filename=filename,
                    original_filename=file.filename,
                    upload_date=datetime.now(),
                    status='processed'
                )
                db.session.add(report)
                db.session.flush()  # Get the report ID
                
                # Save parameters (no reference range - using dictionary)
                for param_data in parameters:
                    parameter = Parameter(
                        report_id=report.id,
                        name=param_data.get('Parameter', ''),
                        value=param_data.get('Value', ''),
                        unit=param_data.get('Unit', '')
                    )
                    db.session.add(parameter)
                
                db.session.commit()
                flash(f'Successfully processed {file.filename}')
                return redirect(url_for('view_report', report_id=report.id))
                
            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
    
    return render_template('upload.html')

@app.route('/report/<int:report_id>')
def view_report(report_id):
    """View a single report"""
    report = Report.query.get_or_404(report_id)
    parameters = Parameter.query.filter_by(report_id=report_id).all()

    # Print debug information to terminal
    if parameters:
        print(f"\n🐛 DEBUG: Dictionary Lookup for Report '{report.original_filename}'")
        print("=" * 80)

        from services.reference_ranges import get_reference_range

        found_count = 0
        total_count = len(parameters)

        for param in parameters:
            range_data = get_reference_range(param.name)
            if range_data:
                found_count += 1
                print(f"✅ {param.name}")
                print(f"   Value: {param.value} {param.unit}")
                print(f"   Dictionary: Found reference range")
                print(f"   Source: {range_data.get('source', 'dictionary')}")
            else:
                print(f"❌ {param.name}")
                print(f"   Value: {param.value} {param.unit}")
                print(f"   Dictionary: Not found - will show 'Reference range not specified'")
            print()

        print(f"📊 Summary: {found_count}/{total_count} parameters found in dictionary ({(found_count/total_count)*100:.1f}%)")
        print("=" * 80)

    # Analyze parameters using dictionary
    analyzed_parameters = []
    pdf_processor = get_pdf_processor()

    for param in parameters:
        analysis = pdf_processor.analyzer.analyze_value_with_dictionary(
            param.name, param.value, param.unit
        )
        analyzed_parameters.append({
            'parameter': param,
            'analysis': analysis
        })

    return render_template('report.html', report=report, parameters=parameters, analyzed_parameters=analyzed_parameters)

@app.route('/compare')
def compare_reports():
    """Compare multiple reports"""
    reports = Report.query.filter_by(status='processed').all()
    return render_template('compare.html', reports=reports)

@app.route('/api/compare', methods=['POST'])
def api_compare_reports():
    """API endpoint for comparing reports"""
    data = request.get_json()
    report_ids = data.get('report_ids', [])

    if len(report_ids) < 2:
        return jsonify({'error': 'At least 2 reports required for comparison'}), 400

    try:
        pdf_processor = get_pdf_processor()
        comparison_data = pdf_processor.compare_multiple_reports(report_ids)
        return jsonify(comparison_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/report/<int:report_id>/flagged')
def api_get_flagged_count(report_id):
    """API endpoint to get count of flagged parameters for a report"""
    try:
        pdf_processor = get_pdf_processor()
        parameters = Parameter.query.filter_by(report_id=report_id).all()

        flagged_count = 0
        for param in parameters:
            if param.value:
                analysis = pdf_processor.analyzer.analyze_value(
                    param.name,
                    param.value,
                    param.unit or '',
                    param.reference_range or ''
                )
                if analysis['status'] in ['high', 'low']:
                    flagged_count += 1

        return jsonify({'flagged_count': flagged_count})
    except Exception as e:
        return jsonify({'error': str(e)}), 500



def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

if __name__ == '__main__':
    app.run(debug=True)
