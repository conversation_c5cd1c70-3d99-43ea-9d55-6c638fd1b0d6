"""
Parameter Analysis Service
Handles parameter analysis using only report-provided information
"""
import re
from typing import Dict, List, Any, Tuple, Optional

class ParameterAnalyzer:
    """Analyzes medical parameters using only information from the reports"""

    def __init__(self):
        # No predefined categories or reference ranges
        # Everything will be based on report data
        pass
    
    def analyze_value_with_report_range(self, param_name: str, value: str, unit: str = '', reference_range: str = '') -> Dict[str, Any]:
        """Analyze a parameter value using only the reference range provided in the report"""
        result = {
            'status': 'normal',
            'flag': '',
            'color': '#28a745',  # green
            'message': '',
            'numeric_value': None,
            'has_reference': bool(reference_range and reference_range.strip())
        }

        # Try to extract numeric value
        numeric_value = self._extract_numeric_value(value)
        if numeric_value is None:
            result['status'] = 'non-numeric'
            result['color'] = '#6c757d'  # gray
            return result

        result['numeric_value'] = numeric_value

        # Only analyze if we have a reference range from the report
        if reference_range and reference_range.strip():
            range_analysis = self._parse_reference_range(reference_range)
            if range_analysis:
                min_val, max_val = range_analysis

                if min_val is not None and numeric_value < min_val:
                    result['status'] = 'low'
                    result['flag'] = '↓'
                    result['color'] = '#007bff'  # blue
                    result['message'] = f'Below reference range ({reference_range})'
                elif max_val is not None and numeric_value > max_val:
                    result['status'] = 'high'
                    result['flag'] = '↑'
                    result['color'] = '#dc3545'  # red
                    result['message'] = f'Above reference range ({reference_range})'
                else:
                    result['message'] = f'Within reference range ({reference_range})'
            else:
                result['message'] = f'Reference range: {reference_range}'
        else:
            result['message'] = 'No reference range provided'

        return result
    
    def _parse_reference_range(self, reference_range: str) -> Optional[Tuple[Optional[float], Optional[float]]]:
        """Parse reference range string to extract min and max values"""
        if not reference_range or not reference_range.strip():
            return None

        range_str = reference_range.strip()

        # Common patterns for reference ranges
        patterns = [
            r'(\d+\.?\d*)\s*-\s*(\d+\.?\d*)',  # "10.5 - 15.2"
            r'(\d+\.?\d*)\s*to\s*(\d+\.?\d*)',  # "10.5 to 15.2"
            r'<\s*(\d+\.?\d*)',  # "< 5.7"
            r'>\s*(\d+\.?\d*)',  # "> 2.5"
            r'≤\s*(\d+\.?\d*)',  # "≤ 5.7"
            r'≥\s*(\d+\.?\d*)',  # "≥ 2.5"
        ]

        # Try range patterns (min - max)
        for pattern in patterns[:2]:
            match = re.search(pattern, range_str, re.IGNORECASE)
            if match:
                try:
                    min_val = float(match.group(1))
                    max_val = float(match.group(2))
                    return (min_val, max_val)
                except ValueError:
                    continue

        # Try single-bound patterns
        # Less than patterns
        for pattern in patterns[2:4]:
            match = re.search(pattern, range_str)
            if match:
                try:
                    max_val = float(match.group(1))
                    return (None, max_val)
                except ValueError:
                    continue

        # Greater than patterns
        for pattern in patterns[4:]:
            match = re.search(pattern, range_str)
            if match:
                try:
                    min_val = float(match.group(1))
                    return (min_val, None)
                except ValueError:
                    continue

        return None
    
    def compare_values(self, value1: str, value2: str, param_name: str = '') -> Dict[str, Any]:
        """Compare two parameter values and return change information"""
        result = {
            'change': 0,
            'change_percent': 0,
            'trend': 'stable',
            'trend_icon': '→',
            'trend_color': '#6c757d',
            'significance': 'none'
        }
        
        num1 = self._extract_numeric_value(value1)
        num2 = self._extract_numeric_value(value2)
        
        if num1 is None or num2 is None:
            return result
        
        # Calculate change
        result['change'] = num2 - num1
        if num1 != 0:
            result['change_percent'] = (result['change'] / num1) * 100
        
        # Determine trend
        if abs(result['change_percent']) < 5:  # Less than 5% change
            result['trend'] = 'stable'
            result['trend_icon'] = '→'
            result['trend_color'] = '#6c757d'
        elif result['change'] > 0:
            result['trend'] = 'increasing'
            result['trend_icon'] = '↗'
            result['trend_color'] = '#dc3545' if result['change_percent'] > 20 else '#fd7e14'
        else:
            result['trend'] = 'decreasing'
            result['trend_icon'] = '↘'
            result['trend_color'] = '#007bff' if result['change_percent'] < -20 else '#20c997'
        
        # Determine significance
        if abs(result['change_percent']) > 50:
            result['significance'] = 'critical'
        elif abs(result['change_percent']) > 20:
            result['significance'] = 'significant'
        elif abs(result['change_percent']) > 10:
            result['significance'] = 'moderate'
        
        return result
    
    def _extract_numeric_value(self, value: str) -> Optional[float]:
        """Extract numeric value from a string"""
        if not value or value.strip() == '':
            return None
        
        # Remove common non-numeric parts
        cleaned = re.sub(r'[<>≤≥]', '', str(value).strip())
        
        # Try to find a number
        match = re.search(r'(\d+\.?\d*)', cleaned)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass
        
        return None
    
    # Main analysis method that should be used
    def analyze_value(self, param_name: str, value: str, unit: str = '', reference_range: str = '') -> Dict[str, Any]:
        """Analyze a parameter value using report-provided reference range"""
        return self.analyze_value_with_report_range(param_name, value, unit, reference_range)
