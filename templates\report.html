{% extends "base.html" %}

{% block title %}Report Details - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-medical"></i> Report Details
            </h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Report Information
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>File Name:</strong></td>
                        <td>{{ report.original_filename }}</td>
                    </tr>
                    <tr>
                        <td><strong>Upload Date:</strong></td>
                        <td>{{ report.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            {% if report.status == 'processed' %}
                                <span class="badge bg-success">Processed</span>
                            {% elif report.status == 'processing' %}
                                <span class="badge bg-warning">Processing</span>
                            {% elif report.status == 'error' %}
                                <span class="badge bg-danger">Error</span>
                            {% else %}
                                <span class="badge bg-secondary">Pending</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Parameters Found:</strong></td>
                        <td>{{ parameters|length }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools"></i> Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('compare_reports') }}" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Compare with Other Reports
                    </a>
                    <button class="btn btn-outline-success" onclick="exportToCSV()">
                        <i class="fas fa-download"></i> Export to CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> Extracted Parameters
                </h6>
            </div>
            <div class="card-body">
                {% if parameters %}
                    <!-- Color Coding Legend -->
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-palette"></i> Color Coding Legend:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <span class="badge bg-success">🟢 Normal</span> - Within reference range<br>
                                <span class="badge bg-danger">🔴 High</span> - Above reference range
                            </div>
                            <div class="col-md-6">
                                <span class="badge bg-info">🔵 Low</span> - Below reference range<br>
                                <span class="badge bg-secondary">⚫ No Range</span> - No reference range available
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="parametersTable">
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for param in parameters %}
                                <tr class="parameter-row" data-param="{{ param.name }}">
                                    <td><strong>{{ param.name }}</strong></td>
                                    <td>
                                        <span class="badge parameter-value"
                                              data-value="{{ param.value or '' }}"
                                              data-param="{{ param.name }}"
                                              data-unit="{{ param.unit or '' }}">
                                            {{ param.value or 'N/A' }}
                                        </span>
                                    </td>
                                    <td>{{ param.unit or '-' }}</td>
                                    <td class="status-cell">
                                        <span class="badge bg-secondary">Analyzing...</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5 class="text-muted">No parameters extracted</h5>
                        <p class="text-muted">The report may need reprocessing or manual review.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
// Dictionary-based parameter analysis (client-side simulation)
const REFERENCE_RANGES = {
    "Glucose": {
        ranges: [
            {max: 70, status: "low", color: "bg-info"},
            {min: 70, max: 100, status: "normal", color: "bg-success"},
            {min: 100, max: 126, status: "borderline", color: "bg-warning"},
            {min: 126, status: "high", color: "bg-danger"}
        ]
    },
    "Hemoglobin": {
        default: {min: 12.0, max: 17.0, status: "normal", color: "bg-success"}
    },
    "Total Cholesterol": {
        ranges: [
            {max: 200, status: "desirable", color: "bg-success"},
            {min: 200, max: 239, status: "borderline", color: "bg-warning"},
            {min: 240, status: "high", color: "bg-danger"}
        ]
    },
    "HDL Cholesterol": {
        ranges: [
            {max: 40, status: "low risk", color: "bg-danger"},
            {min: 40, max: 59, status: "normal", color: "bg-success"},
            {min: 60, status: "high protective", color: "bg-info"}
        ]
    },
    "LDL Cholesterol": {
        ranges: [
            {max: 100, status: "optimal", color: "bg-success"},
            {min: 100, max: 129, status: "near optimal", color: "bg-warning"},
            {min: 130, max: 159, status: "borderline high", color: "bg-warning"},
            {min: 160, status: "high", color: "bg-danger"}
        ]
    },
    "HbA1c": {
        ranges: [
            {max: 5.7, status: "normal", color: "bg-success"},
            {min: 5.7, max: 6.4, status: "prediabetes", color: "bg-warning"},
            {min: 6.5, status: "diabetes", color: "bg-danger"}
        ]
    },
    "Creatinine": {
        default: {min: 0.6, max: 1.3, status: "normal", color: "bg-success"}
    }
};

function analyzeDictionaryParameter(paramName, value, unit) {
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
        return {
            status: 'non-numeric',
            color: 'bg-secondary',
            message: 'Non-numeric value'
        };
    }

    // Look up parameter in dictionary
    const rangeData = REFERENCE_RANGES[paramName];
    if (!rangeData) {
        return {
            status: 'no-reference',
            color: 'bg-secondary',
            message: 'Reference range not specified'
        };
    }

    // Handle multi-tier ranges
    if (rangeData.ranges) {
        for (const tier of rangeData.ranges) {
            const min = tier.min;
            const max = tier.max;

            // Check if value falls in this tier
            let inRange = true;
            if (min !== undefined && numericValue < min) inRange = false;
            if (max !== undefined && numericValue > max) inRange = false;

            if (inRange) {
                return {
                    status: tier.status,
                    color: tier.color,
                    message: tier.status.charAt(0).toUpperCase() + tier.status.slice(1),
                    icon: tier.status.includes('low') ? 'fas fa-arrow-down' :
                          tier.status.includes('high') ? 'fas fa-arrow-up' : null
                };
            }
        }
    }

    // Handle simple range
    if (rangeData.default) {
        const range = rangeData.default;
        const min = range.min;
        const max = range.max;

        if (min !== undefined && numericValue < min) {
            return {
                status: 'low',
                color: 'bg-info',
                icon: 'fas fa-arrow-down',
                message: 'Below reference range'
            };
        } else if (max !== undefined && numericValue > max) {
            return {
                status: 'high',
                color: 'bg-danger',
                icon: 'fas fa-arrow-up',
                message: 'Above reference range'
            };
        } else {
            return {
                status: 'normal',
                color: 'bg-success',
                message: 'Within reference range'
            };
        }
    }

    return {
        status: 'no-reference',
        color: 'bg-secondary',
        message: 'Reference range not specified'
    };
}

function applyParameterAnalysis() {
    const parameterRows = document.querySelectorAll('.parameter-row');

    parameterRows.forEach(row => {
        const paramName = row.dataset.param;
        const valueSpan = row.querySelector('.parameter-value');
        const statusCell = row.querySelector('.status-cell');

        if (valueSpan && statusCell) {
            const value = valueSpan.dataset.value;
            const unit = valueSpan.dataset.unit;

            // Use dictionary-based analysis
            const analysis = analyzeDictionaryParameter(paramName, value, unit);

            // Update the value badge color
            valueSpan.className = `badge ${analysis.color}`;

            if (analysis.icon) {
                valueSpan.innerHTML = `<i class="${analysis.icon}"></i> ${value}`;
            }

            // Update status cell
            statusCell.innerHTML = `<span class="badge ${analysis.color}">${analysis.message}</span>`;

            // Add tooltip with analysis message
            if (analysis.message) {
                valueSpan.title = analysis.message;
                valueSpan.setAttribute('data-bs-toggle', 'tooltip');
            }
        }
    });

    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function applyParameterAnalysis() {
    const parameterValues = document.querySelectorAll('.parameter-value');
    let flaggedCount = 0;

    parameterValues.forEach(badge => {
        const value = badge.getAttribute('data-value');
        const paramName = badge.getAttribute('data-param');
        const unit = badge.getAttribute('data-unit');
        const referenceRange = badge.getAttribute('data-reference');

        if (value && value !== 'N/A') {
            const analysis = analyzeParameter(paramName, value, unit, referenceRange);

            // Update badge styling
            badge.className = `badge ${analysis.color}`;

            if (analysis.icon) {
                badge.innerHTML = `<i class="${analysis.icon}"></i> ${value}`;
            }

            if (analysis.message) {
                badge.setAttribute('title', analysis.message);
            }

            // Add row styling for flagged values
            const row = badge.closest('tr');
            if (analysis.status === 'high' || analysis.status === 'low') {
                row.classList.add(`param-status-${analysis.status}`);
                flaggedCount++;
            } else if (analysis.status === 'normal') {
                row.classList.add('param-status-normal');
            }
        }
    });

    // Show alert if there are flagged parameters
    if (flaggedCount > 0) {
        showFlaggedAlert(flaggedCount);
    }
}

function showFlaggedAlert(count) {
    const alertHtml = `
        <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Attention:</strong> ${count} parameter(s) are outside normal ranges.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const reportCard = document.querySelector('.card-body');
    reportCard.insertAdjacentHTML('afterbegin', alertHtml);
}

function exportToCSV() {
    const table = document.getElementById('parametersTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];

        for (let j = 0; j < cols.length; j++) {
            csvRow.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }

        csv.push(csvRow.join(','));
    }

    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ report.original_filename.replace(".pdf", "_parameters.csv") }}';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Apply analysis when page loads
document.addEventListener('DOMContentLoaded', function() {
    applyParameterAnalysis();
});
</script>
{% endblock %}
