{% extends "base.html" %}

{% block title %}Report Details - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-medical"></i> Report Details
            </h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Report Information
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>File Name:</strong></td>
                        <td>{{ report.original_filename }}</td>
                    </tr>
                    <tr>
                        <td><strong>Upload Date:</strong></td>
                        <td>{{ report.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            {% if report.status == 'processed' %}
                                <span class="badge bg-success">Processed</span>
                            {% elif report.status == 'processing' %}
                                <span class="badge bg-warning">Processing</span>
                            {% elif report.status == 'error' %}
                                <span class="badge bg-danger">Error</span>
                            {% else %}
                                <span class="badge bg-secondary">Pending</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Parameters Found:</strong></td>
                        <td>{{ parameters|length }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools"></i> Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('compare_reports') }}" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Compare with Other Reports
                    </a>
                    <button class="btn btn-outline-success" onclick="exportToCSV()">
                        <i class="fas fa-download"></i> Export to CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> Extracted Parameters
                </h6>
            </div>
            <div class="card-body">
                {% if parameters %}
                    <!-- Color Coding Legend -->
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-palette"></i> Color Coding Legend:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <span class="badge bg-success">🟢 Normal</span> - Within reference range<br>
                                <span class="badge bg-danger">🔴 High</span> - Above reference range
                            </div>
                            <div class="col-md-6">
                                <span class="badge bg-info">🔵 Low</span> - Below reference range<br>
                                <span class="badge bg-secondary">⚫ No Range</span> - No reference range available
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="parametersTable">
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                    <th>Reference Range</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for param in parameters %}
                                <tr class="parameter-row" data-param="{{ param.name }}">
                                    <td><strong>{{ param.name }}</strong></td>
                                    <td>
                                        <span class="badge parameter-value"
                                              data-value="{{ param.value or '' }}"
                                              data-param="{{ param.name }}"
                                              data-unit="{{ param.unit or '' }}"
                                              data-reference="{{ param.reference_range or '' }}">
                                            {{ param.value or 'N/A' }}
                                        </span>
                                    </td>
                                    <td>{{ param.unit or '-' }}</td>
                                    <td>
                                        <small class="text-muted">{{ param.reference_range or 'Not specified' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5 class="text-muted">No parameters extracted</h5>
                        <p class="text-muted">The report may need reprocessing or manual review.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
// Parameter analysis using report-provided reference ranges
function analyzeParameter(paramName, value, unit, referenceRange) {
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
        return {
            status: 'non-numeric',
            color: 'bg-secondary',
            message: 'Non-numeric value'
        };
    }

    // Only analyze if we have a reference range from the report
    if (!referenceRange || referenceRange.trim() === '' || referenceRange === 'Not specified') {
        return {
            status: 'no-reference',
            color: 'bg-secondary',
            message: 'No reference range provided in report'
        };
    }

    const range = parseReferenceRange(referenceRange);
    if (!range) {
        return {
            status: 'unparseable',
            color: 'bg-secondary',
            message: 'Reference range format not recognized'
        };
    }

    const [min, max] = range;

    // Create uniform format for display
    let uniformRange = '';
    if (min !== null && max !== null) {
        uniformRange = `${min} - ${max}`;
    } else if (max !== null) {
        uniformRange = `< ${max}`;
    } else if (min !== null) {
        uniformRange = `> ${min}`;
    }

    if (min !== null && numericValue < min) {
        return {
            status: 'low',
            color: 'bg-info',
            icon: 'fas fa-arrow-down',
            message: `Below reference range (${uniformRange})`
        };
    } else if (max !== null && numericValue > max) {
        return {
            status: 'high',
            color: 'bg-danger',
            icon: 'fas fa-arrow-up',
            message: `Above reference range (${uniformRange})`
        };
    } else {
        return {
            status: 'normal',
            color: 'bg-success',
            message: `Within reference range (${uniformRange})`
        };
    }
}

function parseReferenceRange(referenceRange) {
    if (!referenceRange || !referenceRange.trim()) {
        return null;
    }

    const range = referenceRange.trim();

    // STRICT patterns - only clear numeric ranges (exact matches)
    const strictPatterns = [
        // Standard range patterns (min - max)
        /^(\d+\.?\d*)\s*-\s*(\d+\.?\d*)$/,  // "10.5 - 15.2" (exact match)
        /^(\d+\.?\d*)\s*to\s*(\d+\.?\d*)$/i,  // "10.5 to 15.2" (exact match)
        /^(\d+\.?\d*)\s*–\s*(\d+\.?\d*)$/,  // "10.5 – 15.2" (em dash)

        // Single bound patterns (only if they're the entire string)
        /^<\s*(\d+\.?\d*)$/,  // "< 5.7" (exact match)
        /^>\s*(\d+\.?\d*)$/,  // "> 2.5" (exact match)
        /^≤\s*(\d+\.?\d*)$/,  // "≤ 5.7" (exact match)
        /^≥\s*(\d+\.?\d*)$/,  // "≥ 2.5" (exact match)
        /^<=\s*(\d+\.?\d*)$/,  // "<= 5.7" (exact match)
        /^>=\s*(\d+\.?\d*)$/,  // ">= 2.5" (exact match)
    ];

    // Try range patterns (min - max) first
    for (let i = 0; i < 3; i++) {
        const match = range.match(strictPatterns[i]);
        if (match) {
            const min = parseFloat(match[1]);
            const max = parseFloat(match[2]);
            if (!isNaN(min) && !isNaN(max) && min < max) {
                return [min, max];
            }
        }
    }

    // Try single-bound patterns
    for (let i = 3; i < strictPatterns.length; i++) {
        const match = range.match(strictPatterns[i]);
        if (match) {
            const value = parseFloat(match[1]);
            if (!isNaN(value)) {
                // Determine if it's upper or lower bound
                if (i < 6) {  // < or ≤ or <=
                    return [null, value];
                } else {  // > or ≥ or >=
                    return [value, null];
                }
            }
        }
    }

    // If no strict pattern matches, return null (leave blank)
    return null;
}

function applyParameterAnalysis() {
    const parameterValues = document.querySelectorAll('.parameter-value');
    let flaggedCount = 0;

    parameterValues.forEach(badge => {
        const value = badge.getAttribute('data-value');
        const paramName = badge.getAttribute('data-param');
        const unit = badge.getAttribute('data-unit');
        const referenceRange = badge.getAttribute('data-reference');

        if (value && value !== 'N/A') {
            const analysis = analyzeParameter(paramName, value, unit, referenceRange);

            // Update badge styling
            badge.className = `badge ${analysis.color}`;

            if (analysis.icon) {
                badge.innerHTML = `<i class="${analysis.icon}"></i> ${value}`;
            }

            if (analysis.message) {
                badge.setAttribute('title', analysis.message);
            }

            // Add row styling for flagged values
            const row = badge.closest('tr');
            if (analysis.status === 'high' || analysis.status === 'low') {
                row.classList.add(`param-status-${analysis.status}`);
                flaggedCount++;
            } else if (analysis.status === 'normal') {
                row.classList.add('param-status-normal');
            }
        }
    });

    // Show alert if there are flagged parameters
    if (flaggedCount > 0) {
        showFlaggedAlert(flaggedCount);
    }
}

function showFlaggedAlert(count) {
    const alertHtml = `
        <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Attention:</strong> ${count} parameter(s) are outside normal ranges.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const reportCard = document.querySelector('.card-body');
    reportCard.insertAdjacentHTML('afterbegin', alertHtml);
}

function exportToCSV() {
    const table = document.getElementById('parametersTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];

        for (let j = 0; j < cols.length; j++) {
            csvRow.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }

        csv.push(csvRow.join(','));
    }

    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ report.original_filename.replace(".pdf", "_parameters.csv") }}';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Apply analysis when page loads
document.addEventListener('DOMContentLoaded', function() {
    applyParameterAnalysis();
});
</script>
{% endblock %}
