"""
PDF Processing Service - Refactored from glen4.py
Handles PDF text extraction, cleaning, and parameter extraction using LLM
"""
import fitz
import re
import json
import os
import pandas as pd
from groq import Groq
from typing import List, Dict, Any
from services.database import db, Report, Parameter
from services.parameter_analyzer import ParameterAnalyzer

class PDFProcessor:
    """Main class for processing medical report PDFs"""
    
    def __init__(self):
        self.api_key = os.getenv("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("GROQ_API_KEY environment variable is required")

        self.client = Groq(api_key=self.api_key)
        self.model = os.getenv("GROQ_MODEL", "llama3-70b-8192")
        self.analyzer = ParameterAnalyzer()
    
    def extract_text(self, pdf_path: str) -> str:
        """Extract raw text from PDF file"""
        try:
            import os
            filename = os.path.basename(pdf_path)
            print(f"\n📄 DEBUG: PDF Text Extraction for '{filename}'")
            print("=" * 80)

            doc = fitz.open(pdf_path)
            all_text = []

            for page_num, page in enumerate(doc, 1):
                blocks = page.get_text("blocks")
                print(f"📖 Page {page_num}: Found {len(blocks)} text blocks")

                # Sort blocks by position (top to bottom, left to right)
                blocks.sort(key=lambda b: (b[1], b[0]))
                page_text = "\n".join([b[4].strip() for b in blocks if b[4].strip()])
                all_text.append(page_text)

                # Show first few lines of each page
                lines = page_text.split('\n')[:5]
                print(f"   First few lines: {lines}")

            doc.close()
            raw_text = "\n\n".join(all_text)

            print(f"📊 Extraction Summary:")
            print(f"   Total pages: {len(all_text)}")
            print(f"   Total characters: {len(raw_text)}")
            print(f"   Total lines: {len(raw_text.split(chr(10)))}")
            print("=" * 80)

            return raw_text

        except Exception as e:
            raise Exception(f"Error extracting text from PDF: {str(e)}")
    
    def clean_text(self, raw_text: str) -> str:
        """Clean extracted text by removing headers, footers, and duplicates"""
        print(f"\n🧹 DEBUG: Text Cleaning Process")
        print("=" * 80)

        lines = raw_text.split("\n")
        cleaned = []
        seen = set()
        removed_lines = []

        print(f"📊 Input: {len(lines)} lines")

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line in seen:
                removed_lines.append(f"DUPLICATE: {line}")
                continue

            # Skip common header/footer patterns
            if re.search(r'(Printed on|Page \d+|Ref\. ID|Sample Type|Doctor Name|Report Date)',
                        line, re.IGNORECASE):
                removed_lines.append(f"HEADER/FOOTER: {line}")
                continue

            cleaned.append(line)
            seen.add(line)

        cleaned_text = "\n".join(cleaned)

        print(f"📊 Output: {len(cleaned)} lines (removed {len(lines) - len(cleaned)} lines)")
        print(f"📊 Character reduction: {len(raw_text)} → {len(cleaned_text)} ({len(raw_text) - len(cleaned_text)} chars removed)")

        if removed_lines:
            print(f"🗑️  Sample removed lines (first 5):")
            for removed in removed_lines[:5]:
                print(f"   {removed}")
            if len(removed_lines) > 5:
                print(f"   ... and {len(removed_lines) - 5} more")

        print("=" * 80)

        return cleaned_text
    
    def extract_parameters_with_llm(self, cleaned_text: str) -> List[Dict[str, Any]]:
        """Extract medical parameters using LLM"""
        print(f"\n🤖 DEBUG: LLM Processing")
        print("=" * 80)

        prompt = f"""
You are a medical lab report parser.

Your task is to extract all medically important test parameters from the lab report below.
Return a JSON list where each item has the following fields:

- "Parameter": descriptive test name (e.g., "Hemoglobin", "Total Cholesterol")
- "Value": only the result value (e.g., "13.2" or "Positive")
- "Unit": unit of measurement (e.g., "g/dL", "mg/dL", "%")

Important:
- DO NOT extract reference ranges - only parameter names, values, and units
- DO NOT combine value and unit into one field
- Normalize units like "mg/dl" to "mg/dL"
- Use standard parameter names (e.g., "Total Cholesterol" not "CHOL")
- If unit is missing, leave the field blank
- Include all tests found in the report

Return only valid JSON. No commentary.

Report:
\"\"\"
{cleaned_text}
\"\"\"
"""
        




        print(f"📤 LLM Input (Prompt):")
        print(f"   Model: {self.model}")
        print(f"   Temperature: 0")
        print(f"   Prompt length: {len(prompt)} characters")
        print(f"   Cleaned text length: {len(cleaned_text)} characters")
        print()
        print(f"📋 Full Cleaned Text Sent to LLM:")
        print("-" * 40)
        print(cleaned_text)
        print("-" * 40)
        print()

        try:
            print("🔄 Sending request to Groq API...")
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0
            )

            raw_response = response.choices[0].message.content.strip()
            print(f"📥 LLM Raw Response:")
            print(f"   Response length: {len(raw_response)} characters")
            print(f"   Raw response: {repr(raw_response)}")
            print()

            parsed_result = self.safe_json_extract(raw_response)
            print(f"✅ JSON Parsing Result:")
            print(f"   Successfully parsed: {len(parsed_result)} parameters")
            if parsed_result:
                print(f"   Sample parameter: {parsed_result[0] if parsed_result else 'None'}")
            print("=" * 80)

            return parsed_result

        except Exception as e:
            print(f"❌ LLM Error: {str(e)}")
            print("=" * 80)
            raise Exception(f"Error extracting parameters with LLM: {str(e)}")
    
    def safe_json_extract(self, text: str) -> List[Dict[str, Any]]:
        """Safely extract JSON from LLM response"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            # Try to find JSON array in the text
            match = re.search(r'\[\s*{.*?}\s*]', text, re.DOTALL)
            if match:
                try:
                    # Clean up common JSON formatting issues
                    clean = re.sub(r',\s*]', ']', match.group())
                    return json.loads(clean)
                except:
                    pass
            
            print(f"[!] Failed to parse LLM output:\n{text[:1000]}")
            return []
    
    def process_single_report(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Process a single PDF report and return extracted parameters"""
        try:
            import os
            filename = os.path.basename(pdf_path)
            print(f"\n🚀 DEBUG: Starting Full Pipeline for '{filename}'")
            print("=" * 80)

            # Extract and clean text
            raw_text = self.extract_text(pdf_path)
            cleaned_text = self.clean_text(raw_text)

            # Extract parameters using LLM
            parameters = self.extract_parameters_with_llm(cleaned_text)

            # Print debug information for dictionary lookup
            self._print_dictionary_lookup_debug(parameters, pdf_path)

            # Final summary
            print(f"\n🎯 DEBUG: Pipeline Complete for '{filename}'")
            print("=" * 80)
            print(f"✅ Successfully extracted {len(parameters)} parameters")
            print(f"📊 Pipeline stages completed:")
            print(f"   1. PDF Text Extraction ✅")
            print(f"   2. Text Cleaning ✅")
            print(f"   3. LLM Parameter Extraction ✅")
            print(f"   4. Reference Range Parsing ✅")
            print("=" * 80)

            return parameters

        except Exception as e:
            print(f"\n❌ DEBUG: Pipeline Failed")
            print("=" * 80)
            print(f"Error: {str(e)}")
            print("=" * 80)
            raise Exception(f"Error processing report: {str(e)}")

    def _print_dictionary_lookup_debug(self, parameters: List[Dict[str, Any]], pdf_path: str):
        """Print dictionary lookup debug information to terminal"""
        import os
        from .reference_ranges import get_reference_range

        filename = os.path.basename(pdf_path)

        print(f"\n🐛 DEBUG: Dictionary Lookup for '{filename}'")
        print("=" * 80)

        found_count = 0
        total_count = len(parameters)

        for param in parameters:
            param_name = param.get('Parameter', 'Unknown')
            param_value = param.get('Value', '')
            param_unit = param.get('Unit', '')

            range_data = get_reference_range(param_name)
            if range_data:
                found_count += 1
                print(f"✅ {param_name}")
                print(f"   Value: {param_value} {param_unit}")
                print(f"   Dictionary: Found reference range")
                print(f"   Source: {range_data.get('source', 'dictionary')}")
            else:
                print(f"❌ {param_name}")
                print(f"   Value: {param_value} {param_unit}")
                print(f"   Dictionary: Not found - will show 'Reference range not specified'")
            print()

        print(f"📊 Summary: {found_count}/{total_count} parameters found in dictionary ({(found_count/total_count)*100:.1f}%)")
        print("=" * 80)
    
    def compare_multiple_reports(self, report_ids: List[int]) -> Dict[str, Any]:
        """Compare multiple reports and return aligned data"""
        if len(report_ids) < 2:
            raise ValueError("At least 2 reports required for comparison")
        
        # Get reports from database
        reports = []
        all_parameters = []
        
        for report_id in report_ids:
            report = Report.query.get(report_id)
            if not report:
                raise ValueError(f"Report with ID {report_id} not found")
            
            parameters = Parameter.query.filter_by(report_id=report_id).all()
            param_data = [param.to_dict() for param in parameters]
            
            reports.append(report)
            all_parameters.append(param_data)
        
        # Enhanced comparison with analysis
        aligned_data = self._enhanced_alignment(all_parameters, reports)
        comparison_data = {
            'reports': [report.to_dict() for report in reports],
            'parameters': all_parameters,
            'aligned_data': aligned_data
        }
        
        return comparison_data
    
    def _basic_alignment(self, all_parameters: List[List[Dict]], reports: List[Report]) -> List[Dict]:
        """Basic parameter alignment without LLM (can be enhanced later)"""
        # Create a simple alignment based on parameter names
        all_param_names = set()
        for params in all_parameters:
            for param in params:
                all_param_names.add(param['name'])
        
        aligned_data = []
        for param_name in sorted(all_param_names):
            row = {'Parameter': param_name}
            
            for i, (params, report) in enumerate(zip(all_parameters, reports)):
                report_label = f"Report_{i+1}_{report.original_filename.replace('.pdf', '')}"
                
                # Find matching parameter
                matching_param = next((p for p in params if p['name'] == param_name), None)
                if matching_param:
                    row[report_label] = matching_param['value']
                    row['Unit'] = matching_param.get('unit', '')
                    row['Reference Range'] = matching_param.get('reference_range', '')
                else:
                    row[report_label] = ''
            
            aligned_data.append(row)
        
        return aligned_data

    def _enhanced_alignment(self, all_parameters: List[List[Dict]], reports: List[Report]) -> List[Dict]:
        """Enhanced parameter alignment with analysis and categorization"""
        # Create a simple alignment based on parameter names
        all_param_names = set()
        for params in all_parameters:
            for param in params:
                all_param_names.add(param['name'])

        aligned_data = []
        for param_name in sorted(all_param_names):
            row = {
                'Parameter': param_name,
                'analyses': {},
                'comparisons': {},
                'reference_ranges': {},  # Store reference range for each report
                'units': {}  # Store unit for each report
            }

            values = []
            for i, (params, report) in enumerate(zip(all_parameters, reports)):
                report_label = f"Report_{i+1}_{report.original_filename.replace('.pdf', '')}"

                # Find matching parameter
                matching_param = next((p for p in params if p['name'] == param_name), None)
                if matching_param:
                    value = matching_param['value']
                    unit = matching_param.get('unit', '')
                    ref_range = matching_param.get('reference_range', '')

                    row[report_label] = value
                    row['reference_ranges'][report_label] = ref_range
                    row['units'][report_label] = unit

                    # For backward compatibility, set common fields from first report that has them
                    if 'Unit' not in row and unit:
                        row['Unit'] = unit
                    if 'Reference Range' not in row and ref_range:
                        row['Reference Range'] = ref_range

                    # Analyze the value using dictionary
                    analysis = self.analyzer.analyze_value_with_dictionary(param_name, value, unit)
                    row['analyses'][report_label] = analysis

                    values.append(value)
                else:
                    row[report_label] = ''
                    row['reference_ranges'][report_label] = ''
                    row['units'][report_label] = ''
                    values.append('')

            # Compare values between reports
            if len(values) >= 2 and values[0] and values[1]:
                comparison = self.analyzer.compare_values(values[0], values[1], param_name)
                row['comparisons']['1_to_2'] = comparison

            aligned_data.append(row)

        return aligned_data
