"""
PDF Processing Service - Refactored from glen4.py
Handles PDF text extraction, cleaning, and parameter extraction using LLM
"""
import fitz
import re
import json
import os
import pandas as pd
from groq import Groq
from typing import List, Dict, Any
from services.database import db, Report, Parameter
from services.parameter_analyzer import ParameterAnalyzer

class PDFProcessor:
    """Main class for processing medical report PDFs"""
    
    def __init__(self):
        self.api_key = os.getenv("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("GROQ_API_KEY environment variable is required")

        self.client = Groq(api_key=self.api_key)
        self.model = os.getenv("GROQ_MODEL", "llama3-70b-8192")
        self.analyzer = ParameterAnalyzer()
    
    def extract_text(self, pdf_path: str) -> str:
        """Extract raw text from PDF file"""
        try:
            doc = fitz.open(pdf_path)
            all_text = []
            
            for page in doc:
                blocks = page.get_text("blocks")
                # Sort blocks by position (top to bottom, left to right)
                blocks.sort(key=lambda b: (b[1], b[0]))
                page_text = "\n".join([b[4].strip() for b in blocks if b[4].strip()])
                all_text.append(page_text)
            
            doc.close()
            return "\n\n".join(all_text)
        
        except Exception as e:
            raise Exception(f"Error extracting text from PDF: {str(e)}")
    
    def clean_text(self, raw_text: str) -> str:
        """Clean extracted text by removing headers, footers, and duplicates"""
        lines = raw_text.split("\n")
        cleaned = []
        seen = set()
        
        for line in lines:
            line = line.strip()
            if not line or line in seen:
                continue
            
            # Skip common header/footer patterns
            if re.search(r'(Printed on|Page \d+|Ref\. ID|Sample Type|Doctor Name|Report Date)', 
                        line, re.IGNORECASE):
                continue
            
            cleaned.append(line)
            seen.add(line)
        
        return "\n".join(cleaned)
    
    def extract_parameters_with_llm(self, cleaned_text: str) -> List[Dict[str, Any]]:
        """Extract medical parameters using LLM"""
        prompt = f"""
You are a medical lab report parser.

Your task is to extract all medically important test parameters from the lab report below.
Return a JSON list where each item has the following fields:

- "Parameter": descriptive test name (e.g., "Hemoglobin")
- "Value": only the result value (e.g., "13.2" or "Positive")
- "Unit": unit of measurement (e.g., "g/dL", "mg/dL", "%")
- "Reference Range": standard range (e.g., "13.0 - 17.0" or "<5.7: Normal")

Important:
- DO NOT combine value and unit into one field
- Normalize units like "mg/dl" to "mg/dL"
- If unit or reference is missing, leave the field blank
- Include all tests found in the report

Return only valid JSON. No commentary.

Report:
\"\"\"
{cleaned_text}
\"\"\"
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0
            )
            return self.safe_json_extract(response.choices[0].message.content.strip())
        
        except Exception as e:
            raise Exception(f"Error extracting parameters with LLM: {str(e)}")
    
    def safe_json_extract(self, text: str) -> List[Dict[str, Any]]:
        """Safely extract JSON from LLM response"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            # Try to find JSON array in the text
            match = re.search(r'\[\s*{.*?}\s*]', text, re.DOTALL)
            if match:
                try:
                    # Clean up common JSON formatting issues
                    clean = re.sub(r',\s*]', ']', match.group())
                    return json.loads(clean)
                except:
                    pass
            
            print(f"[!] Failed to parse LLM output:\n{text[:1000]}")
            return []
    
    def process_single_report(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Process a single PDF report and return extracted parameters"""
        try:
            # Extract and clean text
            raw_text = self.extract_text(pdf_path)
            cleaned_text = self.clean_text(raw_text)

            # Extract parameters using LLM
            parameters = self.extract_parameters_with_llm(cleaned_text)

            # Print debug information for reference range parsing
            self._print_reference_range_debug(parameters, pdf_path)

            return parameters

        except Exception as e:
            raise Exception(f"Error processing report: {str(e)}")

    def _print_reference_range_debug(self, parameters: List[Dict[str, Any]], pdf_path: str):
        """Print reference range parsing debug information to terminal"""
        import os
        filename = os.path.basename(pdf_path)

        print(f"\n🐛 DEBUG: Reference Range Parsing for '{filename}'")
        print("=" * 80)

        success_count = 0
        total_count = 0

        for param in parameters:
            ref_range = param.get('Reference Range', '')
            if ref_range and ref_range.strip():
                total_count += 1
                parsed_range = self.analyzer._parse_reference_range(ref_range)

                if parsed_range is not None:
                    success_count += 1
                    min_val, max_val = parsed_range
                    if min_val is not None and max_val is not None:
                        parsed_str = f"Min: {min_val}, Max: {max_val}"
                    elif min_val is not None:
                        parsed_str = f"Min: {min_val}, Max: None"
                    elif max_val is not None:
                        parsed_str = f"Min: None, Max: {max_val}"
                    else:
                        parsed_str = "Empty range"

                    print(f"✅ {param.get('Parameter', 'Unknown')}")
                    print(f"   Original: '{ref_range}'")
                    print(f"   Parsed:   {parsed_str}")
                else:
                    print(f"❌ {param.get('Parameter', 'Unknown')}")
                    print(f"   Original: '{ref_range}'")
                    print(f"   Parsed:   FAILED - Pattern not recognized")
                print()

        if total_count > 0:
            success_rate = (success_count / total_count) * 100
            print(f"📊 Summary: {success_count}/{total_count} ranges parsed successfully ({success_rate:.1f}%)")
        else:
            print("📊 No reference ranges found in this report")
        print("=" * 80)
    
    def compare_multiple_reports(self, report_ids: List[int]) -> Dict[str, Any]:
        """Compare multiple reports and return aligned data"""
        if len(report_ids) < 2:
            raise ValueError("At least 2 reports required for comparison")
        
        # Get reports from database
        reports = []
        all_parameters = []
        
        for report_id in report_ids:
            report = Report.query.get(report_id)
            if not report:
                raise ValueError(f"Report with ID {report_id} not found")
            
            parameters = Parameter.query.filter_by(report_id=report_id).all()
            param_data = [param.to_dict() for param in parameters]
            
            reports.append(report)
            all_parameters.append(param_data)
        
        # Enhanced comparison with analysis
        aligned_data = self._enhanced_alignment(all_parameters, reports)
        comparison_data = {
            'reports': [report.to_dict() for report in reports],
            'parameters': all_parameters,
            'aligned_data': aligned_data
        }
        
        return comparison_data
    
    def _basic_alignment(self, all_parameters: List[List[Dict]], reports: List[Report]) -> List[Dict]:
        """Basic parameter alignment without LLM (can be enhanced later)"""
        # Create a simple alignment based on parameter names
        all_param_names = set()
        for params in all_parameters:
            for param in params:
                all_param_names.add(param['name'])
        
        aligned_data = []
        for param_name in sorted(all_param_names):
            row = {'Parameter': param_name}
            
            for i, (params, report) in enumerate(zip(all_parameters, reports)):
                report_label = f"Report_{i+1}_{report.original_filename.replace('.pdf', '')}"
                
                # Find matching parameter
                matching_param = next((p for p in params if p['name'] == param_name), None)
                if matching_param:
                    row[report_label] = matching_param['value']
                    row['Unit'] = matching_param.get('unit', '')
                    row['Reference Range'] = matching_param.get('reference_range', '')
                else:
                    row[report_label] = ''
            
            aligned_data.append(row)
        
        return aligned_data

    def _enhanced_alignment(self, all_parameters: List[List[Dict]], reports: List[Report]) -> List[Dict]:
        """Enhanced parameter alignment with analysis and categorization"""
        # Create a simple alignment based on parameter names
        all_param_names = set()
        for params in all_parameters:
            for param in params:
                all_param_names.add(param['name'])

        aligned_data = []
        for param_name in sorted(all_param_names):
            row = {
                'Parameter': param_name,
                'analyses': {},
                'comparisons': {},
                'reference_ranges': {},  # Store reference range for each report
                'units': {}  # Store unit for each report
            }

            values = []
            for i, (params, report) in enumerate(zip(all_parameters, reports)):
                report_label = f"Report_{i+1}_{report.original_filename.replace('.pdf', '')}"

                # Find matching parameter
                matching_param = next((p for p in params if p['name'] == param_name), None)
                if matching_param:
                    value = matching_param['value']
                    unit = matching_param.get('unit', '')
                    ref_range = matching_param.get('reference_range', '')

                    row[report_label] = value
                    row['reference_ranges'][report_label] = ref_range
                    row['units'][report_label] = unit

                    # For backward compatibility, set common fields from first report that has them
                    if 'Unit' not in row and unit:
                        row['Unit'] = unit
                    if 'Reference Range' not in row and ref_range:
                        row['Reference Range'] = ref_range

                    # Analyze the value using report-provided reference range
                    analysis = self.analyzer.analyze_value(param_name, value, unit, ref_range)
                    row['analyses'][report_label] = analysis

                    values.append(value)
                else:
                    row[report_label] = ''
                    row['reference_ranges'][report_label] = ''
                    row['units'][report_label] = ''
                    values.append('')

            # Compare values between reports
            if len(values) >= 2 and values[0] and values[1]:
                comparison = self.analyzer.compare_values(values[0], values[1], param_name)
                row['comparisons']['1_to_2'] = comparison

            aligned_data.append(row)

        return aligned_data
