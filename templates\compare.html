{% extends "base.html" %}

{% block title %}Compare Reports - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-chart-line"></i> Compare Reports
            </h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cog"></i> Select Reports to Compare
                </h6>
            </div>
            <div class="card-body">
                <form id="compareForm">
                    <div class="mb-3">
                        <label class="form-label">Available Reports:</label>
                        {% if reports %}
                            {% for report in reports %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       value="{{ report.id }}" id="report{{ report.id }}"
                                       name="report_ids">
                                <label class="form-check-label" for="report{{ report.id }}">
                                    <strong>{{ report.original_filename }}</strong><br>
                                    <small class="text-muted">{{ report.upload_date.strftime('%Y-%m-%d') }}</small>
                                </label>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No processed reports available for comparison.</p>
                        {% endif %}
                    </div>
                    
                    {% if reports %}
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="compareBtn">
                            <i class="fas fa-chart-line"></i> Compare Selected Reports
                        </button>
                    </div>
                    {% endif %}
                </form>
                
                <div id="loadingIndicator" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Comparing reports...</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-table"></i> Comparison Results
                </h6>
            </div>
            <div class="card-body">
                <div id="comparisonResults">
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Select reports to compare</h5>
                        <p class="text-muted">Choose at least 2 reports from the left panel to see the comparison.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('compareForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const checkboxes = document.querySelectorAll('input[name="report_ids"]:checked');
    const reportIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    if (reportIds.length < 2) {
        alert('Please select at least 2 reports to compare.');
        return;
    }
    
    const compareBtn = document.getElementById('compareBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsDiv = document.getElementById('comparisonResults');
    
    // Show loading state
    compareBtn.disabled = true;
    compareBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Comparing...';
    loadingIndicator.style.display = 'block';
    
    // Make API call
    fetch('/api/compare', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ report_ids: reportIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        displayComparisonResults(data);
    })
    .catch(error => {
        console.error('Error:', error);
        resultsDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Error comparing reports: ${error.message}
            </div>
        `;
    })
    .finally(() => {
        // Reset button state
        compareBtn.disabled = false;
        compareBtn.innerHTML = '<i class="fas fa-chart-line"></i> Compare Selected Reports';
        loadingIndicator.style.display = 'none';
    });
});

function displayComparisonResults(data) {
    const resultsDiv = document.getElementById('comparisonResults');
    
    if (!data.aligned_data || data.aligned_data.length === 0) {
        resultsDiv.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                No comparable parameters found between the selected reports.
            </div>
        `;
        return;
    }
    
    // Create table
    let tableHTML = `
        <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
            <table class="table table-hover comparison-table table-sticky-header">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Unit</th>
                        <th>Reference Range</th>
    `;
    
    // Add report columns
    data.reports.forEach((report, index) => {
        tableHTML += `<th>Report ${index + 1}<br><small>${report.original_filename}</small></th>`;
    });

    // Add trend column if comparing exactly 2 reports
    if (data.reports.length === 2) {
        tableHTML += `<th>Trend</th>`;
    }
    
    tableHTML += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    // Add data rows with enhanced styling
    data.aligned_data.forEach(row => {
        tableHTML += '<tr>';

        // Parameter name
        tableHTML += `<td><strong>${row.Parameter}</strong></td>`;

        tableHTML += `<td>${row.Unit || '-'}</td>`;
        tableHTML += `<td><small class="text-muted">${row['Reference Range'] || 'Not specified'}</small></td>`;

        // Add values for each report with analysis
        data.reports.forEach((report, index) => {
            const reportKey = `Report_${index + 1}_${report.original_filename.replace('.pdf', '')}`;
            const value = row[reportKey] || '-';
            const analysis = row.analyses && row.analyses[reportKey];

            if (value === '-') {
                tableHTML += `<td><span class="badge bg-secondary">-</span></td>`;
            } else {
                tableHTML += `<td>${getStatusBadge(value, analysis)}</td>`;
            }
        });

        // Add trend indicator if comparing two reports
        if (data.reports.length === 2 && row.comparisons && row.comparisons['1_to_2']) {
            tableHTML += `<td class="text-center">${getTrendIndicator(row.comparisons['1_to_2'])}</td>`;
        }

        tableHTML += '</tr>';
    });
    
    tableHTML += `
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <button class="btn btn-outline-success" onclick="exportComparisonToCSV()">
                <i class="fas fa-download"></i> Export Comparison to CSV
            </button>
        </div>
    `;
    
    resultsDiv.innerHTML = tableHTML;

    // Store data for export
    window.comparisonData = data;

    // Create visualization
    createComparisonChart(data);
}

function exportComparisonToCSV() {
    if (!window.comparisonData) return;
    
    const data = window.comparisonData;
    let csv = [];
    
    // Header row
    let header = ['Parameter', 'Unit', 'Reference Range'];
    data.reports.forEach((report, index) => {
        header.push(`Report ${index + 1} (${report.original_filename})`);
    });
    csv.push(header.map(h => `"${h}"`).join(','));
    
    // Data rows
    data.aligned_data.forEach(row => {
        let csvRow = [
            `"${row.Parameter}"`,
            `"${row.Unit || ''}"`,
            `"${row['Reference Range'] || ''}"`
        ];
        
        data.reports.forEach((report, index) => {
            const reportKey = `Report_${index + 1}_${report.original_filename.replace('.pdf', '')}`;
            csvRow.push(`"${row[reportKey] || ''}"`);
        });
        
        csv.push(csvRow.join(','));
    });
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'report_comparison.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

function createComparisonChart(data) {
    // Create a simple chart showing all numeric parameters
    if (!data.aligned_data || data.aligned_data.length === 0) return;

    // Filter to only numeric parameters
    const numericParams = data.aligned_data.filter(row => {
        return data.reports.some((report, index) => {
            const reportKey = `Report_${index + 1}_${report.original_filename.replace('.pdf', '')}`;
            const value = row[reportKey];
            return value && !isNaN(parseFloat(value));
        });
    });

    if (numericParams.length === 0) return;

    // Add chart container after the table
    const resultsDiv = document.getElementById('comparisonResults');
    const chartContainer = document.createElement('div');
    chartContainer.innerHTML = `
        <div class="mt-4">
            <h6><i class="fas fa-chart-bar"></i> Parameter Comparison</h6>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Note:</strong> This chart shows all numeric parameters. Different units and scales may make direct comparison difficult.
            </div>
            <div id="comparisonChart" style="height: 400px;"></div>
        </div>
    `;
    resultsDiv.appendChild(chartContainer);

    // Create traces for each report
    const traces = data.reports.map((report, index) => {
        const reportKey = `Report_${index + 1}_${report.original_filename.replace('.pdf', '')}`;

        return {
            x: numericParams.map(row => row.Parameter),
            y: numericParams.map(row => {
                const value = row[reportKey];
                return value ? parseFloat(value) : null;
            }),
            type: 'scatter',
            mode: 'lines+markers',
            name: `Report ${index + 1}`,
            line: { width: 3 },
            marker: { size: 8 },
            connectgaps: false
        };
    });

    const layout = {
        title: 'Parameter Values Comparison',
        xaxis: {
            title: 'Parameters',
            tickangle: -45
        },
        yaxis: { title: 'Values (Mixed Units)' },
        hovermode: 'closest',
        showlegend: true,
        margin: { b: 100, t: 50 }
    };

    Plotly.newPlot('comparisonChart', traces, layout, {responsive: true});
}

function getStatusBadge(value, analysis) {
    if (!analysis) return `<span class="badge bg-primary">${value}</span>`;

    let badgeClass = 'bg-primary';
    let icon = '';

    if (analysis.status === 'high') {
        badgeClass = 'bg-danger';
        icon = '<i class="fas fa-arrow-up"></i> ';
    } else if (analysis.status === 'low') {
        badgeClass = 'bg-info';
        icon = '<i class="fas fa-arrow-down"></i> ';
    } else if (analysis.status === 'normal') {
        badgeClass = 'bg-success';
    }

    return `<span class="badge ${badgeClass}" title="${analysis.message || ''}">${icon}${value}</span>`;
}

function getTrendIndicator(comparison) {
    if (!comparison || comparison.trend === 'stable') {
        return '<span class="text-muted">→</span>';
    }

    let color = comparison.trend_color || '#6c757d';
    let icon = comparison.trend_icon || '→';
    let changeText = '';

    if (comparison.change_percent !== 0) {
        changeText = ` (${comparison.change_percent > 0 ? '+' : ''}${comparison.change_percent.toFixed(1)}%)`;
    }

    return `<span style="color: ${color}" title="Change: ${changeText}">${icon}</span>`;
}


</script>
{% endblock %}
