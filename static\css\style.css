/* Custom styles for Medical Report Tracker */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.badge {
    font-size: 0.75em;
    font-weight: 500;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* File upload styling */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Dashboard cards */
.dashboard-card {
    transition: transform 0.2s ease-in-out;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

/* Parameter table styling */
#parametersTable tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Comparison table styling */
.table-responsive {
    border-radius: 0.5rem;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Status badges */
.status-processed {
    background-color: var(--success-color) !important;
}

.status-processing {
    background-color: var(--warning-color) !important;
}

.status-error {
    background-color: var(--danger-color) !important;
}

.status-pending {
    background-color: #6c757d !important;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.3s ease-out;
}

/* Custom scrollbar for tables */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Enhanced comparison table styles */
.category-indicator {
    border-radius: 2px;
    flex-shrink: 0;
}

.comparison-table .badge {
    font-size: 0.8em;
    padding: 0.4em 0.6em;
    min-width: 60px;
    text-align: center;
}

/* Sticky header for comparison table */
.table-sticky-header thead th {
    position: sticky;
    top: 0;
    background-color: var(--light-color);
    z-index: 10;
    border-bottom: 2px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Improved scrollable table container */
.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

.table-responsive::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #495057;
}

.table-responsive::-webkit-scrollbar-corner {
    background: #f8f9fa;
}

.comparison-table .badge.bg-danger {
    background-color: #dc3545 !important;
    animation: pulse-red 2s infinite;
}

.comparison-table .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.comparison-table .badge.bg-info {
    background-color: #0dcaf0 !important;
    animation: pulse-blue 2s infinite;
}

.comparison-table .badge.bg-success {
    background-color: #198754 !important;
}

/* Trend indicators */
.trend-indicator {
    font-size: 1.2em;
    font-weight: bold;
}

.trend-critical {
    color: #dc3545 !important;
    animation: pulse-red 1.5s infinite;
}

.trend-significant {
    color: #fd7e14 !important;
}

.trend-moderate {
    color: #20c997 !important;
}

/* Pulse animations for critical values */
@keyframes pulse-red {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes pulse-blue {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Category cards */
.category-card {
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Alert indicators */
.alert-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #dc3545;
    animation: pulse-red 1s infinite;
}

/* Parameter status indicators */
.param-status-normal {
    border-left: 3px solid #198754;
}

.param-status-high {
    border-left: 3px solid #dc3545;
}

.param-status-low {
    border-left: 3px solid #0dcaf0;
}

.param-status-critical {
    border-left: 3px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}
