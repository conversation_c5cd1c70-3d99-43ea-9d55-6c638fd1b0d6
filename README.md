# Medical Report Tracker

A web application for tracking and comparing medical lab reports over time using AI-powered parameter extraction.

## 🎯 What It Does

- **Upload PDF Reports**: Drag & drop medical lab reports (blood tests, lipid panels, etc.)
- **AI Extraction**: Automatically extracts parameter names, values, and units
- **Dictionary-Based Analysis**: Uses standardized reference ranges for consistent interpretation
- **Smart Comparison**: Compare parameters across multiple reports with trend analysis
- **Color-Coded Results**: Visual indicators for normal/high/low values based on medical standards
- **Export Data**: Download comparison results as CSV

## 🚀 Quick Setup

### Prerequisites
- Python 3.7+
- Groq API key ([Get one free](https://console.groq.com))

### Installation
```bash
# 1. Clone and setup
git clone <repository-url>
cd medical-report-tracker
python -m venv venv

# 2. Activate environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Configure API key
cp .env.example .env
# Edit .env and add: GROQ_API_KEY=your_api_key_here

# 5. Run application
python app.py
```

Open `http://localhost:5000` in your browser.



## 📋 Reference Range System

**Dictionary-Based Approach**: Uses a standardized reference ranges dictionary for consistent analysis:

✅ **How It Works**:
- Parameter values are analyzed against a built-in medical reference dictionary
- Consistent interpretation regardless of report format variations
- Handles complex multi-tier classifications (e.g., cholesterol levels)
- Supports gender-specific ranges where applicable

✅ **Example Parameters**:
- **Glucose**: Normal (70-100), Borderline (100-126), High (>126) mg/dL
- **Total Cholesterol**: Desirable (<200), Borderline (200-239), High (≥240) mg/dL
- **HDL Cholesterol**: Low Risk (<40), Normal (40-59), High Protective (≥60) mg/dL
- **HbA1c**: Normal (<5.7), Prediabetes (5.7-6.4), Diabetes (≥6.5) %

❌ **Parameters Not in Dictionary**: Show "Reference range not specified"

**Medical Accuracy**: Prevents misinterpretation of complex descriptive ranges from reports.

## 🏗️ Architecture

```
medical-report-tracker/
├── app.py                    # Main Flask application
├── config.py                 # Configuration settings
├── requirements.txt          # Python dependencies
├── .env.example             # Environment template
├── services/
│   ├── pdf_processor.py     # PDF text extraction & AI processing
│   ├── parameter_analyzer.py # Dictionary-based parameter analysis
│   ├── reference_ranges.py  # Standardized reference ranges dictionary
│   └── database.py          # SQLAlchemy models
├── templates/               # HTML templates
├── static/css/              # Styling
└── uploads/                 # PDF storage (auto-created)
```

## 🔧 Technology Stack

- **Backend**: Flask, SQLAlchemy, PyMuPDF
- **AI**: Groq API (Llama 3 70B)
- **Frontend**: Bootstrap 5, Plotly.js
- **Database**: SQLite (development), PostgreSQL (production)

## 📊 Supported Report Types

Works with most standard lab reports containing:
- Blood chemistry panels
- Complete blood counts (CBC)
- Lipid profiles
- Liver/kidney function tests
- Diabetes monitoring (HbA1c, glucose)
- Thyroid function tests

## 🔍 Debug Features

The application includes comprehensive terminal debugging:
- PDF text extraction details
- Text cleaning process
- Full LLM input/output
- Dictionary lookup results for each parameter

## 🚀 Production Deployment

```bash
# Use PostgreSQL
export DATABASE_URL=postgresql://user:password@localhost/medical_reports

# Use Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

## ⚠️ Limitations

- Requires Groq API key (free tier available)
- PDF quality affects extraction accuracy
- English language reports only
- Parameters not in reference dictionary show "Reference range not specified"
- Dictionary contains common parameters but may need expansion for specialized tests

## 📝 License

MIT License - see LICENSE file for details.
