# Medical Report Tracker

A web application for tracking and comparing medical reports over time. Upload PDF reports, extract medical parameters using AI, and compare results across different time periods.

## Features

- **PDF Upload & Processing**: Upload medical report PDFs and automatically extract parameters
- **AI-Powered Extraction**: Uses Groq's LLM API to intelligently parse medical data
- **Parameter Tracking**: Store and organize extracted medical parameters
- **Report Comparison**: Compare parameters across multiple reports
- **Data Export**: Export results to CSV format
- **Web Interface**: Clean, responsive web interface built with Flask and Bootstrap

## Technology Stack

- **Backend**: Python, Flask, SQLAlchemy
- **Frontend**: HTML, CSS, JavaScript, Bootstrap 5
- **AI/ML**: Groq API (Llama 3 70B model)
- **PDF Processing**: PyMuPDF (fitz)
- **Database**: SQLite (default), PostgreSQL (production)

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd medical-report-tracker
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env file and add your GROQ_API_KEY
   ```

5. **Run the application**:
   ```bash
   python app.py
   ```

6. **Access the application**:
   Open your browser and go to `http://localhost:5000`

## Configuration

### Required Environment Variables

- `GROQ_API_KEY`: Your Groq API key (get it from [Groq Console](https://console.groq.com))

### Optional Environment Variables

- `SECRET_KEY`: Flask secret key (auto-generated if not provided)
- `DATABASE_URL`: Database connection string (defaults to SQLite)
- `GROQ_MODEL`: Groq model to use (defaults to llama3-70b-8192)

## Usage

1. **Upload Reports**: Click "Upload Report" and select a PDF medical report
2. **View Results**: After processing, view extracted parameters and values
3. **Compare Reports**: Select multiple reports to compare parameters over time
4. **Export Data**: Download comparison results as CSV files

## Supported Report Types

- Blood test reports
- Lipid profiles
- Liver function tests
- Kidney function tests
- Diabetes panels
- Most standard lab reports with tabular data

## API Endpoints

- `GET /`: Dashboard with recent reports
- `POST /upload`: Upload and process new report
- `GET /report/<id>`: View specific report details
- `GET /compare`: Report comparison interface
- `POST /api/compare`: API endpoint for report comparison

## Architecture

### Modular Design

- `app.py`: Main Flask application
- `config.py`: Configuration management
- `services/pdf_processor.py`: PDF processing and AI extraction
- `services/database.py`: Database models and operations
- `templates/`: HTML templates
- `static/`: CSS, JavaScript, and other static files

### Scalability Considerations

1. **API-based Processing**: Uses external AI API for better accuracy and scalability
2. **Modular Architecture**: Easy to extend and modify components
3. **Database Abstraction**: Can switch from SQLite to PostgreSQL for production
4. **Async Processing**: Can be extended with Celery for background processing
5. **Caching**: Can add Redis for caching frequently accessed data

## Development

### Adding New Features

1. **New Report Types**: Modify the LLM prompt in `pdf_processor.py`
2. **Enhanced Comparison**: Implement advanced alignment algorithms
3. **Visualization**: Add charts and graphs using Plotly
4. **User Management**: Add authentication and user-specific reports

### Testing

```bash
# Run tests (when implemented)
python -m pytest tests/
```

## Production Deployment

1. **Use PostgreSQL**:
   ```bash
   export DATABASE_URL=postgresql://user:password@localhost/medical_reports
   ```

2. **Use Gunicorn**:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:8000 app:app
   ```

3. **Set up reverse proxy** (Nginx recommended)

4. **Enable HTTPS** for secure file uploads

## Limitations

- Requires Groq API key (paid service after free tier)
- PDF quality affects extraction accuracy
- Complex report layouts may need manual review
- Currently supports English language reports

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the existing issues on GitHub
2. Create a new issue with detailed description
3. Include sample reports (anonymized) if relevant
