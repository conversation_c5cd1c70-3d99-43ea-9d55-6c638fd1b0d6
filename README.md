# Medical Report Tracker

A web application for tracking and comparing medical lab reports over time using AI-powered parameter extraction.

## 🎯 What It Does

- **Upload PDF Reports**: Drag & drop medical lab reports (blood tests, lipid panels, etc.)
- **AI Extraction**: Automatically extracts parameters, values, units, and reference ranges
- **Smart Comparison**: Compare parameters across multiple reports with trend analysis
- **Color-Coded Results**: Visual indicators for normal/high/low values based on report ranges
- **Export Data**: Download comparison results as CSV

## 🚀 Quick Setup

### Prerequisites
- Python 3.7+
- Groq API key ([Get one free](https://console.groq.com))

### Installation
```bash
# 1. Clone and setup
git clone <repository-url>
cd medical-report-tracker
python -m venv venv

# 2. Activate environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Configure API key
cp .env.example .env
# Edit .env and add: GROQ_API_KEY=your_api_key_here

# 5. Run application
python app.py
```

Open `http://localhost:5000` in your browser.

## 🎨 Color Coding System

The application uses a strict color coding system for parameter values:

- 🟢 **GREEN**: Value is within the reference range (Normal)
- 🔴 **RED**: Value is above the reference range (High)
- 🔵 **BLUE**: Value is below the reference range (Low)
- ⚫ **GRAY**: No reference range available or non-numeric value

## 📋 Reference Range Processing

**Strict Extraction Policy**: Only clearly defined numeric ranges are extracted:

✅ **Supported Formats**:
- `13.0 - 17.0` → Min: 13.0, Max: 17.0
- `< 5.7` → Max: 5.7
- `> 2.5` → Min: 2.5
- `≤ 100` → Max: 100
- `≥ 40` → Min: 40

❌ **Ignored Formats** (left blank):
- `Normal: 13.0-17.0 (varies by age)`
- `Desirable: <200, Borderline: 200-239`
- `Within normal limits`
- `Negative`

**Uniform Output**: All ranges are displayed in standardized format regardless of original format.

## 🏗️ Architecture

```
medical-report-tracker/
├── app.py                    # Main Flask application
├── config.py                 # Configuration settings
├── requirements.txt          # Python dependencies
├── .env.example             # Environment template
├── services/
│   ├── pdf_processor.py     # PDF text extraction & AI processing
│   ├── parameter_analyzer.py # Reference range parsing & analysis
│   └── database.py          # SQLAlchemy models
├── templates/               # HTML templates
├── static/css/              # Styling
└── uploads/                 # PDF storage (auto-created)
```

## 🔧 Technology Stack

- **Backend**: Flask, SQLAlchemy, PyMuPDF
- **AI**: Groq API (Llama 3 70B)
- **Frontend**: Bootstrap 5, Plotly.js
- **Database**: SQLite (development), PostgreSQL (production)

## 📊 Supported Report Types

Works with most standard lab reports containing:
- Blood chemistry panels
- Complete blood counts (CBC)
- Lipid profiles
- Liver/kidney function tests
- Diabetes monitoring (HbA1c, glucose)
- Thyroid function tests

## 🔍 Debug Features

The application includes comprehensive terminal debugging:
- PDF text extraction details
- Text cleaning process
- Full LLM input/output
- Reference range parsing results

## 🚀 Production Deployment

```bash
# Use PostgreSQL
export DATABASE_URL=postgresql://user:password@localhost/medical_reports

# Use Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

## ⚠️ Limitations

- Requires Groq API key (free tier available)
- PDF quality affects extraction accuracy
- English language reports only
- Reference ranges must be in supported numeric formats

## 📝 License

MIT License - see LICENSE file for details.
