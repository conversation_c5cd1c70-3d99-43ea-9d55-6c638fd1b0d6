{% extends "base.html" %}

{% block title %}Upload Report - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i> Upload Medical Report
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select PDF Report</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".pdf" required>
                        <div class="form-text">
                            Only PDF files are supported. Maximum file size: 16MB.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Processing Information:</strong>
                            <ul class="mb-0 mt-2">
                                <li>The system will extract text from your PDF</li>
                                <li>AI will identify and extract medical parameters</li>
                                <li>Results will be stored for future comparisons</li>
                                <li>Processing may take 30-60 seconds depending on report complexity</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-upload"></i> Upload and Process Report
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </form>
                
                <!-- Processing indicator -->
                <div id="processingIndicator" class="text-center mt-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Processing your report... Please wait.</p>
                </div>
            </div>
        </div>
        
        <!-- Help section -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i> Supported Report Types
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ Supported:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Blood test reports</li>
                            <li><i class="fas fa-check text-success"></i> Lipid profiles</li>
                            <li><i class="fas fa-check text-success"></i> Liver function tests</li>
                            <li><i class="fas fa-check text-success"></i> Kidney function tests</li>
                            <li><i class="fas fa-check text-success"></i> Diabetes panels</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>⚠️ Tips for best results:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-warning"></i> Use clear, high-quality scans</li>
                            <li><i class="fas fa-lightbulb text-warning"></i> Ensure text is readable</li>
                            <li><i class="fas fa-lightbulb text-warning"></i> Include complete report pages</li>
                            <li><i class="fas fa-lightbulb text-warning"></i> Avoid heavily redacted reports</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const processingIndicator = document.getElementById('processingIndicator');
    
    // Show processing indicator
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    processingIndicator.style.display = 'block';
});

// File validation
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (file.type !== 'application/pdf') {
            alert('Please select a PDF file.');
            e.target.value = '';
            return;
        }
        if (file.size > 16 * 1024 * 1024) { // 16MB
            alert('File size must be less than 16MB.');
            e.target.value = '';
            return;
        }
    }
});
</script>
{% endblock %}
