#!/usr/bin/env python3
"""
Setup script for Medical Report Tracker
"""
import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is 3.7 or higher"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")

def create_virtual_environment():
    """Create virtual environment if it doesn't exist"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return
    
    print("📦 Creating virtual environment...")
    subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
    print("✅ Virtual environment created")

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # Determine the correct pip path
    if os.name == 'nt':  # Windows
        pip_path = Path("venv/Scripts/pip")
    else:  # Unix/Linux/macOS
        pip_path = Path("venv/bin/pip")
    
    subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)
    print("✅ Dependencies installed")

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_path = Path(".env")
    if env_path.exists():
        print("✅ .env file already exists")
        return
    
    print("📝 Creating .env file from template...")
    with open(".env.example", "r") as template:
        content = template.read()
    
    with open(".env", "w") as env_file:
        env_file.write(content)
    
    print("✅ .env file created")
    print("⚠️  Please edit .env file and add your GROQ_API_KEY")

def create_directories():
    """Create necessary directories"""
    directories = ["uploads", "static/css", "static/js"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created")

def main():
    """Main setup function"""
    print("🏥 Medical Report Tracker Setup")
    print("=" * 40)
    
    try:
        check_python_version()
        create_directories()
        create_virtual_environment()
        install_dependencies()
        create_env_file()
        
        print("\n" + "=" * 40)
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file and add your GROQ_API_KEY")
        print("2. Activate virtual environment:")
        
        if os.name == 'nt':  # Windows
            print("   venv\\Scripts\\activate")
        else:  # Unix/Linux/macOS
            print("   source venv/bin/activate")
        
        print("3. Run the application:")
        print("   python app.py")
        print("4. Open http://localhost:5000 in your browser")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
