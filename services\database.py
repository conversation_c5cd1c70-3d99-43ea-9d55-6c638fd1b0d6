"""
Database models for the Medical Report Tracker
"""
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Report(db.Model):
    """Model for storing report metadata"""
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(50), default='pending')  # pending, processing, processed, error
    patient_id = db.Column(db.String(100))  # Optional patient identifier
    report_date = db.Column(db.Date)  # Date of the medical report
    
    # Relationship to parameters
    parameters = db.relationship('Parameter', backref='report', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Report {self.original_filename}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'upload_date': self.upload_date.isoformat() if self.upload_date else None,
            'status': self.status,
            'patient_id': self.patient_id,
            'report_date': self.report_date.isoformat() if self.report_date else None
        }

class Parameter(db.Model):
    """Model for storing extracted parameters from reports"""
    id = db.Column(db.Integer, primary_key=True)
    report_id = db.Column(db.Integer, db.ForeignKey('report.id'), nullable=False)
    name = db.Column(db.String(200), nullable=False)  # Parameter name (e.g., "Hemoglobin")
    value = db.Column(db.String(100))  # Parameter value (e.g., "13.2")
    unit = db.Column(db.String(50))  # Unit (e.g., "g/dL")
    # Note: reference_range removed - now using standardized dictionary

    def __repr__(self):
        return f'<Parameter {self.name}: {self.value} {self.unit}>'

    def to_dict(self):
        return {
            'id': self.id,
            'report_id': self.report_id,
            'name': self.name,
            'value': self.value,
            'unit': self.unit
        }
