import fitz
import re
import json
import os
import pandas as pd
from groq import Groq

# === CONFIG ===
api_key = os.getenv("GROQ_API_KEY")
client = Groq(api_key=api_key)

MODEL = "llama3-70b-8192"

# === Step 1: Extract raw PDF text ===
def extract_text(pdf_path):
    doc = fitz.open(pdf_path)
    all_text = []
    for page in doc:
        blocks = page.get_text("blocks")
        blocks.sort(key=lambda b: (b[1], b[0]))
        page_text = "\n".join([b[4].strip() for b in blocks if b[4].strip()])
        all_text.append(page_text)
    return "\n\n".join(all_text)

# === Step 2: Clean extracted text ===
def clean_text(raw_text):
    lines = raw_text.split("\n")
    cleaned = []
    seen = set()
    for line in lines:
        line = line.strip()
        if not line or line in seen:
            continue
        if re.search(r'(Printed on|Page \d+|Ref\. ID|Sample Type|Doctor Name|Report Date)', line, re.IGNORECASE):
            continue
        cleaned.append(line)
        seen.add(line)
    return "\n".join(cleaned)

# === Step 3: Extract parameters from a single report ===
def extract_parameters_with_llm(cleaned_text):
    prompt = f"""
You are a medical lab report parser.

Your task is to extract all medically important test parameters from the lab report below.
Return a JSON list where each item has the following fields:

- "Parameter": descriptive test name (e.g., "Hemoglobin")
- "Value": only the result value (e.g., "13.2" or "Positive")
- "Unit": unit of measurement (e.g., "g/dL", "mg/dL", "%")
- "Reference Range": standard range (e.g., "13.0 - 17.0" or "<5.7: Normal")

Important:
- DO NOT combine value and unit into one field
- Normalize units like "mg/dl" to "mg/dL"
- If unit or reference is missing, leave the field blank
- Include all tests found in the report

Return only valid JSON. No commentary.

Report:
\"\"\"
{cleaned_text}
\"\"\"
"""
    response = client.chat.completions.create(
        model=MODEL,
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )
    return safe_json_extract(response.choices[0].message.content.strip())

# === Step 4: Safe JSON parser ===
def safe_json_extract(text):
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        match = re.search(r'\[\s*{.*?}\s*]', text, re.DOTALL)
        if match:
            try:
                clean = re.sub(r',\s*]', ']', match.group())
                return json.loads(clean)
            except:
                pass
        print("[!] Failed to parse output:\n", text[:1000])
        return []

# === Step 5: Alignment prompt for both reports ===
def build_alignment_prompt(json_a, json_b, label_a="Report A", label_b="Report B"):
    return f"""
You are a medical lab report comparison expert.

You are given two sets of lab parameters extracted from two different reports of the same patient. Each test has:
- Parameter name
- Value
- Unit
- Reference Range

Your job is to align and compare the parameters using the following rules:

1. If two tests refer to the same biological measurement (e.g., "Triglyceride" and "Triglycerides", or "S.G.O.T." and "SGOT"), merge them under a normalized name.
2. Normalize all units and reference ranges where possible.
3. Include all parameters from both reports, even if a test appears in only one report.
4. Leave value fields blank if a test is missing from one of the reports.
5. Prefer simpler or more standard names when resolving duplicates.
6. Do NOT confuse different measurements (e.g., "HDL" ≠ "LDL").

Output ONLY this JSON format:

[
  {{
    "Parameter": "Hemoglobin",
    "Unit": "g/dL",
    "Reference Range": "13.0 - 17.0",
    "{label_a}": "15.4",
    "{label_b}": "14.6"
  }},
  ...
]

Do not include any explanation or commentary.

Report A:
{json.dumps(json_a)}

Report B:
{json.dumps(json_b)}
"""

# === Step 6: Run LLM for alignment ===
def align_parameter_sets(params_a, params_b, label_a, label_b):
    prompt = build_alignment_prompt(params_a, params_b, label_a, label_b)
    response = client.chat.completions.create(
        model=MODEL,
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )
    return safe_json_extract(response.choices[0].message.content.strip())

# === Step 7: Complete comparison pipeline ===
def compare_two_reports(pdf1_path, pdf2_path):
    label1 = os.path.basename(pdf1_path).replace(".pdf", "")
    label2 = os.path.basename(pdf2_path).replace(".pdf", "")

    raw1 = extract_text(pdf1_path)
    raw2 = extract_text(pdf2_path)

    cleaned1 = clean_text(raw1)
    cleaned2 = clean_text(raw2)

    parsed1 = extract_parameters_with_llm(cleaned1)
    parsed2 = extract_parameters_with_llm(cleaned2)

    aligned_data = align_parameter_sets(parsed1, parsed2, label1, label2)
    return pd.DataFrame(aligned_data)

# === CLI ===
if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python compare_reports_aligned.py report1.pdf report2.pdf")
        sys.exit(1)

    result = compare_two_reports(sys.argv[1], sys.argv[2])
    result.to_csv("comparison_result_aligned.csv", index=False)
    print("[✓] Saved aligned comparison to comparison_result_aligned.csv")
