{% extends "base.html" %}

{% block title %}Debug Reference Ranges - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-bug"></i> Reference Range Parsing Debug
            </h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Debug Information:</strong> This page shows how the system parses reference ranges from all your reports.
            Use this to identify patterns that aren't being recognized correctly.
        </div>
    </div>
</div>

{% if debug_data %}
    {% for report_debug in debug_data %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-medical"></i> {{ report_debug.report.original_filename }}
                        <small class="text-muted">({{ report_debug.report.upload_date.strftime('%Y-%m-%d %H:%M') }})</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Original Range</th>
                                    <th>Parsed Result</th>
                                    <th>Status</th>
                                    <th>Analysis</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for param_debug in report_debug.parameters %}
                                <tr class="{% if param_debug.parse_success %}table-success{% else %}table-danger{% endif %}">
                                    <td><strong>{{ param_debug.parameter.name }}</strong></td>
                                    <td>
                                        <code style="background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px;">
                                            {{ param_debug.parameter.reference_range }}
                                        </code>
                                    </td>
                                    <td>
                                        {% if param_debug.parsed_range %}
                                            {% set min_val, max_val = param_debug.parsed_range %}
                                            {% if min_val is not none and max_val is not none %}
                                                <span class="badge bg-success">Range: {{ min_val }} - {{ max_val }}</span>
                                            {% elif min_val is not none %}
                                                <span class="badge bg-info">Min: {{ min_val }}</span>
                                            {% elif max_val is not none %}
                                                <span class="badge bg-info">Max: {{ max_val }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Empty range</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-danger">Parse failed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if param_debug.parse_success %}
                                            <i class="fas fa-check-circle text-success"></i> Success
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger"></i> Failed
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if not param_debug.parse_success %}
                                            <small class="text-muted">
                                                Pattern not recognized. Consider adding support for this format.
                                            </small>
                                        {% else %}
                                            <small class="text-success">
                                                Successfully parsed and can be used for analysis.
                                            </small>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Summary for this report -->
                    {% set total_params = report_debug.parameters|length %}
                    {% set successful_params = report_debug.parameters|selectattr('parse_success')|list|length %}
                    {% set failed_params = total_params - successful_params %}
                    
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>Total Parameters</h6>
                                        <h4>{{ total_params }}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h6>Successfully Parsed</h6>
                                        <h4>{{ successful_params }}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h6>Parse Failures</h6>
                                        <h4>{{ failed_params }}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    
    <!-- Overall Summary -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i> Overall Parsing Statistics
                    </h5>
                </div>
                <div class="card-body">
                    {% set all_params = debug_data|sum(attribute='parameters')|list %}
                    {% set total_all = all_params|length %}
                    {% set success_all = all_params|selectattr('parse_success')|list|length %}
                    {% set fail_all = total_all - success_all %}
                    
                    <div class="row">
                        <div class="col-md-3">
                            <h6>Total Parameters with Ranges</h6>
                            <h3>{{ total_all }}</h3>
                        </div>
                        <div class="col-md-3">
                            <h6>Successfully Parsed</h6>
                            <h3 class="text-success">{{ success_all }}</h3>
                        </div>
                        <div class="col-md-3">
                            <h6>Parse Failures</h6>
                            <h3 class="text-danger">{{ fail_all }}</h3>
                        </div>
                        <div class="col-md-3">
                            <h6>Success Rate</h6>
                            <h3 class="text-info">
                                {% if total_all > 0 %}
                                    {{ "%.1f"|format((success_all / total_all) * 100) }}%
                                {% else %}
                                    N/A
                                {% endif %}
                            </h3>
                        </div>
                    </div>
                    
                    {% if fail_all > 0 %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Improvement Needed:</strong> {{ fail_all }} reference ranges couldn't be parsed. 
                        Consider enhancing the parsing patterns to support these formats.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
{% else %}
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Reference Ranges Found</h5>
                    <p class="text-muted">No processed reports with reference ranges were found in the database.</p>
                    <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload a Report
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endif %}

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-code"></i> Supported Reference Range Patterns
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Range Patterns:</h6>
                        <ul class="list-unstyled">
                            <li><code>10.5 - 15.2</code> → Min: 10.5, Max: 15.2</li>
                            <li><code>10.5 to 15.2</code> → Min: 10.5, Max: 15.2</li>
                            <li><code>10.5-15.2</code> → Min: 10.5, Max: 15.2</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Single Bound Patterns:</h6>
                        <ul class="list-unstyled">
                            <li><code>&lt; 5.7</code> → Max: 5.7</li>
                            <li><code>&gt; 2.5</code> → Min: 2.5</li>
                            <li><code>≤ 5.7</code> → Max: 5.7</li>
                            <li><code>≥ 2.5</code> → Min: 2.5</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
