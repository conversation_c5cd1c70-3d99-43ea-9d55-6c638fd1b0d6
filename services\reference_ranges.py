"""
Reference Ranges Dictionary for Medical Parameters

This module contains a standardized dictionary of reference ranges for common medical parameters.
The dictionary supports multiple range formats and provides consistent interpretation across all reports.

Range Format Options:
1. Simple range: {"min": 13.0, "max": 17.0}
2. Upper limit only: {"max": 5.7}
3. Lower limit only: {"min": 2.5}
4. Multi-tier ranges: [{"max": 200, "status": "desirable"}, {"min": 200, "max": 239, "status": "borderline"}, ...]
5. Gender-specific: {"male": {"min": 13.0, "max": 17.0}, "female": {"min": 12.0, "max": 15.5}}
6. Age-specific: {"adult": {"min": 70, "max": 100}, "child": {"min": 60, "max": 95}}

Status Options: "normal", "low", "high", "optimal", "borderline", "desirable", "risk"
Color Options: "green", "red", "blue", "yellow", "orange", "gray"
"""

# Standard Reference Ranges Dictionary
REFERENCE_RANGES = {
    # Blood Chemistry - Basic Metabolic Panel
    "Glucose": {
        "ranges": [
            {"max": 70, "status": "low", "color": "blue"},
            {"min": 70, "max": 100, "status": "normal", "color": "green"},
            {"min": 100, "max": 126, "status": "borderline", "color": "yellow"},
            {"min": 126, "status": "high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Fasting glucose levels"
    },
    
    "Haemoglobin": {
        "gender_specific": {
            "male": {"min": 13.0, "max": 17.0, "status": "normal", "color": "green"},
            "female": {"min": 12.0, "max": 15.5, "status": "normal", "color": "green"}
        },
        "default": {"min": 12.0, "max": 17.0, "status": "normal", "color": "green"},
        "unit": "g/dL",
        "description": "Haemoglobin concentration"
    },


    
    "Total Cholesterol": {
        "ranges": [
            {"max": 200, "status": "desirable", "color": "green"},
            {"min": 200, "max": 239, "status": "borderline", "color": "yellow"},
            {"min": 240, "status": "high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Total cholesterol levels"
    },
    
    "HDL Cholesterol": {
        "ranges": [
            {"max": 40, "status": "low", "color": "red"},  # Low HDL = High cardiovascular risk
            {"min": 40, "max": 59, "status": "normal", "color": "green"},
            {"min": 60, "status": "high", "color": "blue"}  # High HDL = Protective
        ],
        "unit": "mg/dL",
        "description": "High-density lipoprotein cholesterol"
    },
    
    "LDL Cholesterol": {
        "ranges": [
            {"max": 100, "status": "optimal", "color": "green"},
            {"min": 100, "max": 129, "status": "near_optimal", "color": "yellow"},
            {"min": 130, "max": 159, "status": "borderline_high", "color": "orange"},
            {"min": 160, "status": "high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Low-density lipoprotein cholesterol"
    }
}


def get_reference_range(parameter_name: str, gender: str = None, age: int = None) -> dict:
    """
    Get reference range for a parameter from the dictionary.
    
    Args:
        parameter_name: Name of the parameter
        gender: Optional gender for gender-specific ranges
        age: Optional age for age-specific ranges
    
    Returns:
        Dictionary with range information or None if not found
    """
    # Normalize parameter name (case-insensitive, handle variations)
    param_key = None
    for key in REFERENCE_RANGES.keys():
        if key.lower() == parameter_name.lower():
            param_key = key
            break
    
    if not param_key:
        return None
    
    range_data = REFERENCE_RANGES[param_key]
    
    # Handle gender-specific ranges
    if "gender_specific" in range_data and gender:
        gender_key = gender.lower()
        if gender_key in range_data["gender_specific"]:
            return {
                "range": range_data["gender_specific"][gender_key],
                "unit": range_data.get("unit", ""),
                "description": range_data.get("description", ""),
                "source": "dictionary_gender_specific"
            }
    
    # Handle multi-tier ranges
    if "ranges" in range_data:
        return {
            "ranges": range_data["ranges"],
            "unit": range_data.get("unit", ""),
            "description": range_data.get("description", ""),
            "source": "dictionary_multi_tier"
        }
    
    # Handle default range
    if "default" in range_data:
        return {
            "range": range_data["default"],
            "unit": range_data.get("unit", ""),
            "description": range_data.get("description", ""),
            "source": "dictionary_default"
        }
    
    return None


def add_parameter_range(parameter_name: str, range_data: dict) -> bool:
    """
    Add a new parameter range to the dictionary.
    
    Args:
        parameter_name: Name of the parameter
        range_data: Dictionary containing range information
    
    Returns:
        True if added successfully, False otherwise
    """
    try:
        REFERENCE_RANGES[parameter_name] = range_data
        return True
    except Exception:
        return False


def list_available_parameters() -> list:
    """
    Get list of all available parameters in the dictionary.
    
    Returns:
        List of parameter names
    """
    return list(REFERENCE_RANGES.keys())
