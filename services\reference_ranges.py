"""
Reference Ranges Dictionary for Medical Parameters

This module contains a standardized dictionary of reference ranges for common medical parameters.
The dictionary supports multiple range formats and provides consistent interpretation across all reports.

Range Format Options:
1. Simple range: {"min": 13.0, "max": 17.0}
2. Upper limit only: {"max": 5.7}
3. Lower limit only: {"min": 2.5}
4. Multi-tier ranges: [{"max": 200, "status": "desirable"}, {"min": 200, "max": 239, "status": "borderline"}, ...]
5. Gender-specific: {"male": {"min": 13.0, "max": 17.0}, "female": {"min": 12.0, "max": 15.5}}
6. Age-specific: {"adult": {"min": 70, "max": 100}, "child": {"min": 60, "max": 95}}

Status Options: "normal", "low", "high", "optimal", "borderline", "desirable", "risk"
Color Options: "green", "red", "blue", "yellow", "orange", "gray"
"""

# Standard Reference Ranges Dictionary
REFERENCE_RANGES = {
    # Blood Chemistry - Basic Metabolic Panel
    "Glucose": {
        "ranges": [
            {"max": 70, "status": "low", "color": "blue"},
            {"min": 70, "max": 100, "status": "normal", "color": "green"},
            {"min": 100, "max": 126, "status": "borderline", "color": "yellow"},
            {"min": 126, "status": "high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Fasting glucose levels"
    },
    
    "Hemoglobin": {
        "gender_specific": {
            "male": {"min": 13.0, "max": 17.0, "status": "normal", "color": "green"},
            "female": {"min": 12.0, "max": 15.5, "status": "normal", "color": "green"}
        },
        "default": {"min": 12.0, "max": 17.0, "status": "normal", "color": "green"},
        "unit": "g/dL",
        "description": "Hemoglobin concentration"
    },
    
    "Total Cholesterol": {
        "ranges": [
            {"max": 200, "status": "desirable", "color": "green"},
            {"min": 200, "max": 239, "status": "borderline", "color": "yellow"},
            {"min": 240, "status": "high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Total cholesterol levels"
    },
    
    "HDL Cholesterol": {
        "ranges": [
            {"max": 40, "status": "low_risk", "color": "red"},  # Low HDL = High cardiovascular risk
            {"min": 40, "max": 59, "status": "normal", "color": "green"},
            {"min": 60, "status": "high_protective", "color": "blue"}  # High HDL = Protective
        ],
        "unit": "mg/dL",
        "description": "High-density lipoprotein cholesterol"
    },
    
    "LDL Cholesterol": {
        "ranges": [
            {"max": 100, "status": "optimal", "color": "green"},
            {"min": 100, "max": 129, "status": "near_optimal", "color": "yellow"},
            {"min": 130, "max": 159, "status": "borderline_high", "color": "orange"},
            {"min": 160, "status": "high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Low-density lipoprotein cholesterol"
    },
    
    "Triglycerides": {
        "ranges": [
            {"max": 150, "status": "normal", "color": "green"},
            {"min": 150, "max": 199, "status": "borderline", "color": "yellow"},
            {"min": 200, "max": 499, "status": "high", "color": "orange"},
            {"min": 500, "status": "very_high", "color": "red"}
        ],
        "unit": "mg/dL",
        "description": "Triglyceride levels"
    },
    
    "Creatinine": {
        "gender_specific": {
            "male": {"min": 0.7, "max": 1.3, "status": "normal", "color": "green"},
            "female": {"min": 0.6, "max": 1.1, "status": "normal", "color": "green"}
        },
        "default": {"min": 0.6, "max": 1.3, "status": "normal", "color": "green"},
        "unit": "mg/dL",
        "description": "Serum creatinine"
    },
    
    "HbA1c": {
        "ranges": [
            {"max": 5.7, "status": "normal", "color": "green"},
            {"min": 5.7, "max": 6.4, "status": "prediabetes", "color": "yellow"},
            {"min": 6.5, "status": "diabetes", "color": "red"}
        ],
        "unit": "%",
        "description": "Glycated hemoglobin"
    },
    
    # Placeholder entries - to be filled with actual values
    "Albumin": {
        "default": {"min": 3.5, "max": 5.0, "status": "normal", "color": "green"},
        "unit": "g/dL",
        "description": "Serum albumin - PLACEHOLDER VALUES"
    },
    
    "Bilirubin Total": {
        "default": {"max": 1.2, "status": "normal", "color": "green"},
        "unit": "mg/dL",
        "description": "Total bilirubin - PLACEHOLDER VALUES"
    },
    
    "ALT": {
        "default": {"max": 40, "status": "normal", "color": "green"},
        "unit": "U/L",
        "description": "Alanine aminotransferase - PLACEHOLDER VALUES"
    },
    
    "AST": {
        "default": {"max": 40, "status": "normal", "color": "green"},
        "unit": "U/L",
        "description": "Aspartate aminotransferase - PLACEHOLDER VALUES"
    },
    
    "Platelet Count": {
        "default": {"min": 150, "max": 450, "status": "normal", "color": "green"},
        "unit": "K/uL",
        "description": "Platelet count - PLACEHOLDER VALUES"
    },
    
    "White Blood Cell Count": {
        "default": {"min": 4.0, "max": 11.0, "status": "normal", "color": "green"},
        "unit": "K/uL",
        "description": "WBC count - PLACEHOLDER VALUES"
    },
    
    "Red Blood Cell Count": {
        "gender_specific": {
            "male": {"min": 4.5, "max": 5.9, "status": "normal", "color": "green"},
            "female": {"min": 4.1, "max": 5.1, "status": "normal", "color": "green"}
        },
        "default": {"min": 4.1, "max": 5.9, "status": "normal", "color": "green"},
        "unit": "M/uL",
        "description": "RBC count - PLACEHOLDER VALUES"
    },
    
    "Hematocrit": {
        "gender_specific": {
            "male": {"min": 41, "max": 53, "status": "normal", "color": "green"},
            "female": {"min": 36, "max": 46, "status": "normal", "color": "green"}
        },
        "default": {"min": 36, "max": 53, "status": "normal", "color": "green"},
        "unit": "%",
        "description": "Hematocrit - PLACEHOLDER VALUES"
    },
    
    "TSH": {
        "default": {"min": 0.4, "max": 4.0, "status": "normal", "color": "green"},
        "unit": "mIU/L",
        "description": "Thyroid stimulating hormone - PLACEHOLDER VALUES"
    },
    
    "Vitamin D": {
        "ranges": [
            {"max": 20, "status": "deficient", "color": "red"},
            {"min": 20, "max": 30, "status": "insufficient", "color": "yellow"},
            {"min": 30, "status": "sufficient", "color": "green"}
        ],
        "unit": "ng/mL",
        "description": "25-hydroxyvitamin D - PLACEHOLDER VALUES"
    }
}


def get_reference_range(parameter_name: str, gender: str = None, age: int = None) -> dict:
    """
    Get reference range for a parameter from the dictionary.
    
    Args:
        parameter_name: Name of the parameter
        gender: Optional gender for gender-specific ranges
        age: Optional age for age-specific ranges
    
    Returns:
        Dictionary with range information or None if not found
    """
    # Normalize parameter name (case-insensitive, handle variations)
    param_key = None
    for key in REFERENCE_RANGES.keys():
        if key.lower() == parameter_name.lower():
            param_key = key
            break
    
    if not param_key:
        return None
    
    range_data = REFERENCE_RANGES[param_key]
    
    # Handle gender-specific ranges
    if "gender_specific" in range_data and gender:
        gender_key = gender.lower()
        if gender_key in range_data["gender_specific"]:
            return {
                "range": range_data["gender_specific"][gender_key],
                "unit": range_data.get("unit", ""),
                "description": range_data.get("description", ""),
                "source": "dictionary_gender_specific"
            }
    
    # Handle multi-tier ranges
    if "ranges" in range_data:
        return {
            "ranges": range_data["ranges"],
            "unit": range_data.get("unit", ""),
            "description": range_data.get("description", ""),
            "source": "dictionary_multi_tier"
        }
    
    # Handle default range
    if "default" in range_data:
        return {
            "range": range_data["default"],
            "unit": range_data.get("unit", ""),
            "description": range_data.get("description", ""),
            "source": "dictionary_default"
        }
    
    return None


def add_parameter_range(parameter_name: str, range_data: dict) -> bool:
    """
    Add a new parameter range to the dictionary.
    
    Args:
        parameter_name: Name of the parameter
        range_data: Dictionary containing range information
    
    Returns:
        True if added successfully, False otherwise
    """
    try:
        REFERENCE_RANGES[parameter_name] = range_data
        return True
    except Exception:
        return False


def list_available_parameters() -> list:
    """
    Get list of all available parameters in the dictionary.
    
    Returns:
        List of parameter names
    """
    return list(REFERENCE_RANGES.keys())
