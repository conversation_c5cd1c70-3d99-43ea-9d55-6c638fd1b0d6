# Environment variables for Medical Report Tracker

# Flask Configuration
SECRET_KEY=your-secret-key-here
FLASK_ENV=development

# Database Configuration
DATABASE_URL=sqlite:///medical_reports.db

# Groq API Configuration (Required)
GROQ_API_KEY=your-groq-api-key-here
GROQ_MODEL=llama3-70b-8192

# Optional: For production deployment
# DATABASE_URL=postgresql://user:password@localhost/medical_reports
# FLASK_ENV=production
