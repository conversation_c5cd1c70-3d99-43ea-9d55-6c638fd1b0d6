#!/usr/bin/env python3
"""
Demo script for Medical Report Tracker
Shows how to use the PDF processing functionality programmatically
"""
import os
import sys
from pathlib import Path
from services.pdf_processor import PDFProcessor

def demo_pdf_processing():
    """Demonstrate PDF processing capabilities"""
    print("🏥 Medical Report Tracker - Demo")
    print("=" * 40)
    
    # Check if GROQ_API_KEY is set
    if not os.getenv("GROQ_API_KEY"):
        print("❌ GROQ_API_KEY environment variable is required")
        print("Please set it in your .env file or environment")
        return False
    
    try:
        # Initialize PDF processor
        print("📦 Initializing PDF processor...")
        processor = PDFProcessor()
        print("✅ PDF processor initialized")
        
        # Check for sample PDF files
        sample_files = list(Path(".").glob("*.pdf"))
        if not sample_files:
            print("\n📄 No PDF files found in current directory")
            print("To test the functionality:")
            print("1. Place a medical report PDF in this directory")
            print("2. Run this demo script again")
            print("3. Or use the web interface at http://localhost:5000")
            return True
        
        print(f"\n📄 Found {len(sample_files)} PDF file(s):")
        for i, pdf_file in enumerate(sample_files, 1):
            print(f"  {i}. {pdf_file.name}")
        
        # Process the first PDF file
        pdf_file = sample_files[0]
        print(f"\n🔍 Processing: {pdf_file.name}")
        
        # Extract text
        print("  📝 Extracting text...")
        raw_text = processor.extract_text(str(pdf_file))
        print(f"  ✅ Extracted {len(raw_text)} characters")
        
        # Clean text
        print("  🧹 Cleaning text...")
        cleaned_text = processor.clean_text(raw_text)
        print(f"  ✅ Cleaned text: {len(cleaned_text)} characters")
        
        # Extract parameters
        print("  🤖 Extracting parameters with AI...")
        parameters = processor.extract_parameters_with_llm(cleaned_text)
        print(f"  ✅ Extracted {len(parameters)} parameters")
        
        # Display results
        if parameters:
            print("\n📊 Extracted Parameters:")
            print("-" * 60)
            for i, param in enumerate(parameters, 1):
                name = param.get('Parameter', 'Unknown')
                value = param.get('Value', 'N/A')
                unit = param.get('Unit', '')
                ref_range = param.get('Reference Range', '')
                
                print(f"{i:2d}. {name}")
                print(f"    Value: {value} {unit}")
                if ref_range:
                    print(f"    Reference: {ref_range}")
                print()
        else:
            print("⚠️  No parameters extracted. This could be due to:")
            print("   - PDF format not supported")
            print("   - Text quality issues")
            print("   - Non-medical document")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return False

def demo_web_interface():
    """Show how to start the web interface"""
    print("\n🌐 Web Interface Demo")
    print("-" * 40)
    print("To use the full web interface:")
    print("1. Run: python app.py")
    print("2. Open: http://localhost:5000")
    print("3. Upload PDF reports through the web interface")
    print("4. Compare multiple reports")
    print("5. Export results to CSV")

def main():
    """Main demo function"""
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        success = demo_pdf_processing()
        demo_web_interface()
        
        if success:
            print("\n🎉 Demo completed successfully!")
        else:
            print("\n❌ Demo encountered issues")
            
    except ImportError:
        print("❌ Required modules not installed")
        print("Please run: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
