{% extends "base.html" %}

{% block title %}Dashboard - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Version1
        </h1>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Reports</h5>
                        <h2>{{ reports|length }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-medical fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Quick Upload</h5>
                        <a href="{{ url_for('upload_file') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-upload"></i> Upload New Report
                        </a>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-plus-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Compare Reports</h5>
                        <a href="{{ url_for('compare_reports') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-chart-line"></i> Start Comparison
                        </a>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-balance-scale fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Recent Reports
                </h5>
            </div>
            <div class="card-body">
                {% if reports %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>File Name</th>
                                    <th>Upload Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in reports %}
                                <tr>
                                    <td>
                                        <i class="fas fa-file-pdf text-danger"></i>
                                        {{ report.original_filename }}
                                    </td>
                                    <td>{{ report.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if report.status == 'processed' %}
                                            <span class="badge bg-success">Processed</span>
                                            <span class="badge bg-info ms-1" id="flagged-{{ report.id }}">
                                                <i class="fas fa-flag"></i> <span class="flagged-count">0</span>
                                            </span>
                                        {% elif report.status == 'processing' %}
                                            <span class="badge bg-warning">Processing</span>
                                        {% elif report.status == 'error' %}
                                            <span class="badge bg-danger">Error</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Pending</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('view_report', report_id=report.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No reports uploaded yet</h5>
                        <p class="text-muted">Upload your first medical report to get started.</p>
                        <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Upload Report
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load flagged parameter counts for each processed report
document.addEventListener('DOMContentLoaded', function() {
    const flaggedBadges = document.querySelectorAll('[id^="flagged-"]');

    flaggedBadges.forEach(badge => {
        const reportId = badge.id.replace('flagged-', '');

        fetch(`/api/report/${reportId}/flagged`)
            .then(response => response.json())
            .then(data => {
                const countSpan = badge.querySelector('.flagged-count');
                const count = data.flagged_count || 0;
                countSpan.textContent = count;

                if (count > 0) {
                    badge.classList.remove('bg-info');
                    badge.classList.add('bg-warning');
                    badge.setAttribute('title', `${count} parameter(s) flagged as abnormal`);
                } else {
                    badge.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading flagged count:', error);
                badge.style.display = 'none';
            });
    });
});
</script>
{% endblock %}
